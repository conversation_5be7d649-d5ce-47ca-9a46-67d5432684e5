#!/usr/bin/env python3

# =============================================================================
# CODY - Advanced Context-Aware AI-Powered CLI Agent
# An autonomous coding assistant with multi-language support, debugging,
# and advanced file handling capabilities
# =============================================================================

#------------------------------------------------------------------------------
# 1. IMPORTS (Organized by category)
# -----------------------------------------------------------------------------

# Standard library importsu
import os
import sys
import json
import re
import time
import subprocess
import threading
import asyncio
import concurrent.futures
import logging
import hashlib
import pickle
from pathlib import Path
from textwrap import dedent
from typing import List, Dict, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import tempfile
import shutil
import glob

# Fix Unicode encoding issues on Windows
if sys.platform.startswith('win'):
    import codecs
    # Set UTF-8 encoding for stdout/stderr to prevent Unicode errors
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except Exception:
            pass

# Third-party imports
from openai import OpenAI, Stream
from openai.types.chat import ChatCompletionChunk
from openai.types.chat.chat_completion_chunk import ChoiceDelta
from openai.types.completion_usage import CompletionUsage
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Rich console imports
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.style import Style
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.syntax import Syntax
from rich.tree import Tree
from rich.live import Live

# Prompt toolkit imports
from prompt_toolkit import PromptSession
from prompt_toolkit.styles import Style as PromptStyle
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.history import InMemoryHistory

# Advanced imports with fallbacks
try:
    from thefuzz import fuzz, process as fuzzy_process
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False
    print("Warning: thefuzz not available. Install with: pip install thefuzz python-levenshtein")

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    print("Warning: tree-sitter not available. Install with: pip install tree-sitter")

try:
    import requests
    from bs4 import BeautifulSoup
    WEB_SEARCH_AVAILABLE = True
except ImportError:
    WEB_SEARCH_AVAILABLE = False
    print("Warning: Web search dependencies not available. Install with: pip install requests beautifulsoup4")

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: Gemini AI not available. Install with: pip install google-generativeai")

try:
    import pylint.lint
    import ast
    STATIC_ANALYSIS_AVAILABLE = True
except ImportError:
    STATIC_ANALYSIS_AVAILABLE = False
    print("Warning: Static analysis tools not available. Install with: pip install pylint astroid")

# Import our custom modules
try:
    from core.nlp_processor import NLPProcessor, Intent
    from core.code_analyzer import CodeAnalyzer
    from core.autonomous_debugger import AutonomousDebugger
    from core.web_search_rag import WebSearchRAG
    from core.task_manager import MultiThreadedTaskManager, TaskPriority
    from core.workflow_engine import EnhancedWorkflowEngine, ChainOfThoughtReasoner
    from core.general_intelligence import GeneralIntelligence
    from core.performance_core import UltraPerformanceCore, PerformanceTask
    from core.codebase_awareness import CodebaseAwareness
    from core.terminal_fs_agent import TerminalFileSystemAgent
    from core.iterative_workflow_engine import IterativeWorkflowEngine, WorkflowContext
    from core.advanced_pattern_search import AdvancedPatternSearchEngine, SearchMode, AdvancedSearchResult
    from core.smart_code_replacer import SmartCodeReplacer, ReplacementMode, ReplacementResult, ReplacementPreview
    from core.smart_file_search import SmartFileSearchEngine, SearchType, SmartSearchResult
    from core.auto_planner import AutoPlanner, PlanningStrategy, ExecutionPlan, Task, TaskStatus
    from core.input_fixer import InputFixer, FixType, Language, FixResult
    from core.file_indexer import FileIndexer, IndexType, FileType, SearchResult
    ADVANCED_MODULES_AVAILABLE = True
except ImportError as e:
    ADVANCED_MODULES_AVAILABLE = False
    print(f"Warning: Advanced modules not available: {e}")
    print("Some advanced features will be disabled.")

# -----------------------------------------------------------------------------
# 2. ENUMS AND TYPE DEFINITIONS
# -----------------------------------------------------------------------------

class TaskType(Enum):
    """Types of tasks the agent can perform."""
    CODE_GENERATION = "code_generation"
    FILE_OPERATION = "file_operation"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    TESTING = "testing"
    SEARCH = "search"
    ANALYSIS = "analysis"
    TERMINAL_COMMAND = "terminal_command"

class LanguageType(Enum):
    """Supported programming languages."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    CPP = "cpp"
    JAVA = "java"
    GO = "go"
    RUST = "rust"
    C = "c"
    HTML = "html"
    CSS = "css"
    SQL = "sql"
    BASH = "bash"
    POWERSHELL = "powershell"
    JSON = "json"
    YAML = "yaml"
    MARKDOWN = "markdown"
    TEXT = "text"
    

class AIModel(Enum):
    """Available AI models."""
    DEEPSEEK_CHAT = "deepseek-chat"
    DEEPSEEK_REASONER = "deepseek-reasoner"
    GEMINI_2_0_FLASH = "gemini-2.0-flash"
    GEMINI_1_5_FLASH = "gemini-1.5-flash"

# -----------------------------------------------------------------------------
# 3. CONFIGURATION CONSTANTS
# -----------------------------------------------------------------------------

# File operation limits
MAX_FILES_IN_ADD_DIR: int = 1000
MAX_FILE_SIZE_IN_ADD_DIR: int = 5_000_000  # 5MB
MAX_FILE_CONTENT_SIZE_CREATE: int = 5_000_000  # 5MB
MAX_MULTIPLE_READ_SIZE: int = 100_000  # 100KB total limit for multiple file reads

# Fuzzy matching thresholds
MIN_FUZZY_SCORE: int = 80  # Minimum score for file path fuzzy matching
MIN_EDIT_SCORE: int = 85   # Minimum score for code edit fuzzy matching

# Command prefixes
ADD_COMMAND_PREFIX: str = "/add "
COMMIT_COMMAND_PREFIX: str = "/commit "
GIT_BRANCH_COMMAND_PREFIX: str = "/git branch "

# Conversation management
MAX_HISTORY_MESSAGES: int = 50
MAX_CONTEXT_FILES: int = 5
ESTIMATED_MAX_TOKENS: int = 66000  # Conservative estimate for context window
TOKENS_PER_MESSAGE_ESTIMATE: int = 200  # Average tokens per message
TOKENS_PER_FILE_KB: int = 300  # Estimated tokens per KB of file content
CONTEXT_WARNING_THRESHOLD: float = 0.8  # Warn when 80% of context is used
AGGRESSIVE_TRUNCATION_THRESHOLD: float = 0.9  # More aggressive truncation at 90%

# Enhanced Model configuration with Mistral AI integration
MODEL_MAPPING = {
    "deepseek-chat": "deepseek-chat",
    "deepseek-reasoner": "deepseek-reasoner",
    "gemini-2.0-flash": "gemini-2.0-flash",
    "gemini-1.5-flash": "gemini-2.0-flash-lite",
    "mistral-large": "mistral-large-latest",
    "mistral-medium": "mistral-medium-latest",
    "mistral-small": "mistral-small-latest",
    "mistral-codestral": "codestral-latest",
    "claude": "claude-3-sonnet-20240229",  # Fallback mapping
    "gpt-4": "gpt-4-turbo-preview"  # Fallback mapping
}

# Model provider configuration
MODEL_PROVIDERS = {
    "deepseek-chat": "deepseek",
    "deepseek-reasoner": "deepseek",
    "gemini-2.0-flash": "gemini",
    "gemini-1.5-flash": "gemini",
    "mistral-large": "mistral",
    "mistral-medium": "mistral",
    "mistral-small": "mistral",
    "mistral-codestral": "mistral"
}

# Mistral API Keys (rotating for load balancing)
MISTRAL_API_KEYS = [
    "g15Wh3HTEPYwkOkUmQgfMnY9oajh3Vw1",
    "SBvzOPDnFcovyxhn1ZjM5DHzupsHsm4y",
    "Obju64whxwASUGntLMXOXZuDnnwLvh0M"
]

DEFAULT_MODEL: str = "deepseek-chat"
REASONER_MODEL: str = "deepseek-reasoner"
FALLBACK_MODEL: str = "deepseek-chat"  # Always available fallback

# Performance optimization settings
PERFORMANCE_CONFIG = {
    "api_timeout": 30.0,
    "max_retries": 3,
    "cache_size_mb": 256,
    "concurrent_requests": 5,
    "response_streaming": True,
    "connection_pooling": True
}

# Advanced features configuration
ENABLE_PREDICTIVE_PREFETCHING: bool = True
ENABLE_AUTONOMOUS_DEBUGGING: bool = True
ENABLE_CONTEXT_COMPRESSION: bool = True
ENABLE_MULTI_THREADING: bool = True
CACHE_SIZE_MB: int = 100
MAX_CONCURRENT_TASKS: int = 4

# Web search configuration
MAX_SEARCH_RESULTS: int = 5
SEARCH_TIMEOUT: int = 10

# Static analysis configuration
ENABLE_LINTING: bool = True
ENABLE_TYPE_CHECKING: bool = True

# File exclusion patterns
EXCLUDED_FILES: set = {
    ".DS_Store", "Thumbs.db", ".gitignore", ".python-version", "uv.lock", 
    ".uv", "uvenv", ".uvenv", ".venv", "venv", "__pycache__", ".pytest_cache", 
    ".coverage", ".mypy_cache", "node_modules", "package-lock.json", "yarn.lock", 
    "pnpm-lock.yaml", ".next", ".nuxt", "dist", "build", ".cache", ".parcel-cache", 
    ".turbo", ".vercel", ".output", ".contentlayer", "out", "coverage", 
    ".nyc_output", "storybook-static", ".env", ".env.local", ".env.development", 
    ".env.production", ".git", ".svn", ".hg", "CVS"
}

EXCLUDED_EXTENSIONS: set = {
    ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".webp", ".avif", 
    ".mp4", ".webm", ".mov", ".mp3", ".wav", ".ogg", ".zip", ".tar", 
    ".gz", ".7z", ".rar", ".exe", ".dll", ".so", ".dylib", ".bin", 
    ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pyc", 
    ".pyo", ".pyd", ".egg", ".whl", ".uv", ".uvenv", ".db", ".sqlite", 
    ".sqlite3", ".log", ".idea", ".vscode", ".map", ".chunk.js", 
    ".chunk.css", ".min.js", ".min.css", ".bundle.js", ".bundle.css", 
    ".cache", ".tmp", ".temp", ".ttf", ".otf", ".woff", ".woff2", ".eot"
}

# -----------------------------------------------------------------------------
# 4. ADVANCED DATA CLASSES AND MODELS
# -----------------------------------------------------------------------------

@dataclass
class TaskContext:
    """Context information for a task."""
    task_id: str
    task_type: TaskType
    language: Optional[LanguageType] = None
    files_involved: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    priority: int = 1
    created_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CodeAnalysisResult:
    """Result of code analysis."""
    file_path: str
    language: LanguageType
    functions: List[Dict[str, Any]] = field(default_factory=list)
    classes: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[Dict[str, Any]] = field(default_factory=list)
    complexity_score: float = 0.0
    lines_of_code: int = 0

@dataclass
class SearchResult:
    """Result from web search or code search."""
    title: str
    url: Optional[str] = None
    content: str = ""
    relevance_score: float = 0.0
    source_type: str = "web"
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CacheEntry:
    """Cache entry for storing computed results."""
    key: str
    value: Any
    created_at: float = field(default_factory=time.time)
    access_count: int = 0
    size_bytes: int = 0

class FileToCreate(BaseModel):
    path: str
    content: str
    language: Optional[str] = None
    template: Optional[str] = None

class FileToEdit(BaseModel):
    path: str
    original_snippet: str
    new_snippet: str
    line_number: Optional[int] = None
    confidence: float = 1.0

class DebugContext(BaseModel):
    """Context for debugging operations."""
    error_message: str
    file_path: str
    line_number: Optional[int] = None
    stack_trace: Optional[str] = None
    suggested_fixes: List[str] = Field(default_factory=list)
    confidence: float = 0.0

# -----------------------------------------------------------------------------
# 5. GLOBAL STATE MANAGEMENT
# -----------------------------------------------------------------------------

# Initialize Rich console and prompt session with enhanced features
console = Console(record=True)  # Enable recording for better debugging
command_history = InMemoryHistory()
command_completer = WordCompleter([
    '/add', '/commit', '/git', '/help', '/clear', '/exit', '/search',
    '/debug', '/refactor', '/test', '/analyze', '/context', '/reasoner'
])

prompt_session = PromptSession(
    style=PromptStyle.from_dict({
        'prompt': '#0066ff bold',
        'completion-menu.completion': 'bg:#1e3a8a fg:#ffffff',
        'completion-menu.completion.current': 'bg:#3b82f6 fg:#ffffff bold',
    }),
    history=command_history,
    completer=command_completer
)

# Global base directory for operations (default: current working directory)
base_dir: Path = Path.cwd()

# Enhanced context management
class AgentState:
    """Centralized state management for the agent."""
    def __init__(self):
        self.git_context = {
            'enabled': False,
            'skip_staging': False,
            'branch': None
        }
        self.model_context = {
            'current_model': DEFAULT_MODEL,
            'is_reasoner': False,
            'available_models': [AIModel.DEEPSEEK_CHAT, AIModel.DEEPSEEK_REASONER, AIModel.GEMINI_2_0_FLASH, AIModel.GEMINI_1_5_FLASH] if GEMINI_AVAILABLE else [AIModel.DEEPSEEK_CHAT, AIModel.DEEPSEEK_REASONER]
        }
        self.security_context = {
            'require_powershell_confirmation': True,
            'require_bash_confirmation': True,
            'safe_mode': True
        }
        self.task_queue = deque()
        self.active_tasks = {}
        self.cache = {}
        self.performance_metrics = defaultdict(list)
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=MAX_CONCURRENT_TASKS)

# Enhanced Intelligence Classes
class EnhancedContextManager:
    """Manages context with intelligent understanding and memory."""

    def __init__(self):
        self.conversation_context = []
        self.project_context = {}
        self.user_preferences = {}
        self.code_patterns = {}
        self.recent_actions = deque(maxlen=50)

    def add_context(self, context_type: str, data: Any):
        """Add context with intelligent categorization."""
        timestamp = time.time()
        context_entry = {
            'type': context_type,
            'data': data,
            'timestamp': timestamp,
            'relevance_score': self._calculate_relevance(context_type, data)
        }
        self.conversation_context.append(context_entry)
        self._update_patterns(context_type, data)

    def _calculate_relevance(self, context_type: str, data: Any) -> float:
        """Calculate relevance score for context prioritization."""
        base_score = 1.0
        if context_type == 'code_change':
            base_score = 1.5
        elif context_type == 'error':
            base_score = 2.0
        elif context_type == 'user_preference':
            base_score = 1.8
        return base_score

    def _update_patterns(self, context_type: str, data: Any):
        """Update learned patterns from context."""
        if context_type not in self.code_patterns:
            self.code_patterns[context_type] = []
        self.code_patterns[context_type].append(data)

    def get_relevant_context(self, query: str, max_items: int = 10) -> List[Dict]:
        """Get most relevant context for a query."""
        # Simple relevance scoring - can be enhanced with ML
        relevant = sorted(
            self.conversation_context,
            key=lambda x: x['relevance_score'],
            reverse=True
        )
        return relevant[:max_items]

class ChainOfThoughtReasoner:
    """Enhanced reasoning engine with chain-of-thought capabilities."""

    def __init__(self):
        self.reasoning_steps = []
        self.decision_tree = {}

    def reason_through_problem(self, problem: str, context: Dict = None) -> Dict:
        """Apply chain-of-thought reasoning to solve problems."""
        reasoning_chain = {
            'problem': problem,
            'context': context or {},
            'steps': [],
            'solution': None,
            'confidence': 0.0,
            'alternatives': []
        }

        # Step 1: Problem Analysis
        analysis = self._analyze_problem(problem)
        reasoning_chain['steps'].append({
            'step': 'analysis',
            'description': 'Problem analysis and decomposition',
            'result': analysis
        })

        # Step 2: Context Evaluation
        context_eval = self._evaluate_context(context or {})
        reasoning_chain['steps'].append({
            'step': 'context_evaluation',
            'description': 'Context and constraint evaluation',
            'result': context_eval
        })

        # Step 3: Solution Generation
        solutions = self._generate_solutions(problem, analysis, context_eval)
        reasoning_chain['steps'].append({
            'step': 'solution_generation',
            'description': 'Generate possible solutions',
            'result': solutions
        })

        # Step 4: Solution Evaluation
        best_solution = self._evaluate_solutions(solutions)
        reasoning_chain['solution'] = best_solution
        reasoning_chain['confidence'] = best_solution.get('confidence', 0.0)
        reasoning_chain['alternatives'] = [s for s in solutions if s != best_solution]

        return reasoning_chain

    def _analyze_problem(self, problem: str) -> Dict:
        """Analyze and decompose the problem."""
        return {
            'complexity': 'medium',  # Simple heuristic
            'domain': 'coding',
            'keywords': problem.lower().split(),
            'estimated_effort': 'medium'
        }

    def _evaluate_context(self, context: Dict) -> Dict:
        """Evaluate context and constraints."""
        return {
            'constraints': context.get('constraints', []),
            'resources': context.get('resources', []),
            'preferences': context.get('preferences', {})
        }

    def _generate_solutions(self, problem: str, analysis: Dict, context: Dict) -> List[Dict]:
        """Generate multiple solution approaches."""
        solutions = [
            {
                'approach': 'direct',
                'description': 'Direct implementation approach',
                'confidence': 0.8,
                'steps': ['analyze', 'implement', 'test']
            },
            {
                'approach': 'incremental',
                'description': 'Incremental development approach',
                'confidence': 0.9,
                'steps': ['plan', 'implement_incrementally', 'test_continuously']
            }
        ]
        return solutions

    def _evaluate_solutions(self, solutions: List[Dict]) -> Dict:
        """Evaluate and select the best solution."""
        return max(solutions, key=lambda x: x.get('confidence', 0.0))

class AdaptiveLearningSystem:
    """System that learns from user interactions and improves over time."""

    def __init__(self):
        self.user_patterns = {}
        self.success_metrics = {}
        self.failure_patterns = {}
        self.preferences = {}

    def record_interaction(self, interaction_type: str, data: Dict, success: bool):
        """Record user interaction for learning."""
        timestamp = time.time()
        interaction = {
            'type': interaction_type,
            'data': data,
            'success': success,
            'timestamp': timestamp
        }

        if interaction_type not in self.user_patterns:
            self.user_patterns[interaction_type] = []
        self.user_patterns[interaction_type].append(interaction)

        # Update success metrics
        if interaction_type not in self.success_metrics:
            self.success_metrics[interaction_type] = {'success': 0, 'total': 0}

        self.success_metrics[interaction_type]['total'] += 1
        if success:
            self.success_metrics[interaction_type]['success'] += 1

    def get_success_rate(self, interaction_type: str) -> float:
        """Get success rate for an interaction type."""
        if interaction_type not in self.success_metrics:
            return 0.0

        metrics = self.success_metrics[interaction_type]
        if metrics['total'] == 0:
            return 0.0

        return metrics['success'] / metrics['total']

    def suggest_improvements(self, interaction_type: str) -> List[str]:
        """Suggest improvements based on learned patterns."""
        success_rate = self.get_success_rate(interaction_type)
        suggestions = []

        if success_rate < 0.7:
            suggestions.append(f"Consider using preview mode for {interaction_type}")
            suggestions.append(f"Enable backup files for {interaction_type}")

        if success_rate < 0.5:
            suggestions.append(f"Break down {interaction_type} into smaller steps")
            suggestions.append(f"Use execution plans for complex {interaction_type}")

        return suggestions

class IntelligentSuggestionEngine:
    """Engine that provides intelligent suggestions based on context."""

    def __init__(self):
        self.suggestion_rules = self._initialize_rules()
        self.context_patterns = {}

    def _initialize_rules(self) -> Dict:
        """Initialize suggestion rules."""
        return {
            'file_operations': {
                'patterns': ['create_file', 'edit_file'],
                'suggestions': ['Use preview mode', 'Enable backups', 'Consider Git commit']
            },
            'search_operations': {
                'patterns': ['search_code', 'advanced_pattern_search'],
                'suggestions': ['Build file index first', 'Use semantic search', 'Try hybrid mode']
            },
            'refactoring': {
                'patterns': ['smart_replace_code', 'refactor_code'],
                'suggestions': ['Preview changes first', 'Create execution plan', 'Generate tests']
            }
        }

    def get_suggestions(self, current_action: str, context: Dict = None) -> List[str]:
        """Get intelligent suggestions for current action."""
        suggestions = []

        # Rule-based suggestions
        for category, rules in self.suggestion_rules.items():
            if any(pattern in current_action for pattern in rules['patterns']):
                suggestions.extend(rules['suggestions'])

        # Context-based suggestions
        if context:
            if context.get('has_errors'):
                suggestions.append('Consider using debug_code function')
            if context.get('large_codebase'):
                suggestions.append('Build file index for better performance')
            if context.get('multiple_files'):
                suggestions.append('Use create_execution_plan for coordination')

        return list(set(suggestions))  # Remove duplicates

    def suggest_next_actions(self, completed_action: str, context: Dict = None) -> List[str]:
        """Suggest logical next actions based on what was just completed."""
        next_actions = []

        if 'create_file' in completed_action:
            next_actions.extend(['generate_tests', 'git_add', 'analyze_code'])
        elif 'edit_file' in completed_action:
            next_actions.extend(['run_tests', 'git_commit', 'debug_code'])
        elif 'search' in completed_action:
            next_actions.extend(['smart_replace_code', 'refactor_code', 'analyze_code'])
        elif 'build_file_index' in completed_action:
            next_actions.extend(['search_file_index', 'advanced_pattern_search'])

        return next_actions

# Initialize global state
agent_state = AgentState()

# Initialize AI clients with enhanced error handling
load_dotenv()

def create_robust_openai_client():
    """Create OpenAI client with robust error handling and validation."""
    try:
        api_key = os.getenv('DEEPSEEK_API_KEY', '***********************************')
        base_url = os.getenv('DEEPSEEK_BASE_URL', "https://api.deepseek.com")

        if not api_key or api_key == 'your-api-key-here':
            console.print("[yellow]⚠ Warning: Using default API key. Set DEEPSEEK_API_KEY for production use.[/yellow]")

        client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=30.0,  # Add timeout
            max_retries=3   # Add retry logic
        )

        # Test the client with a simple request
        try:
            test_response = client.models.list()
            console.print("[green]✅ DeepSeek API client validated successfully![/green]")
        except Exception as e:
            console.print(f"[yellow]⚠ DeepSeek API validation warning: {e}[/yellow]")

        return client

    except Exception as e:
        console.print(f"[red]✗ Failed to create DeepSeek client: {e}[/red]")
        # Return a mock client that will fail gracefully
        return None

client = create_robust_openai_client()

# Initialize Gemini client with enhanced error handling
def create_robust_gemini_client():
    """Create Gemini client with robust error handling and validation."""
    if not GEMINI_AVAILABLE:
        return None

    try:
        # Use provided API key or fallback to environment variable
        gemini_api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs')

        if not gemini_api_key or gemini_api_key == 'your-gemini-api-key':
            console.print("[yellow]⚠ Warning: No valid Gemini API key found.[/yellow]")
            return None

        genai.configure(api_key=gemini_api_key)

        # Try both model versions for compatibility
        try:
            client = genai.GenerativeModel('gemini-2.0-flash-exp')
            # Test the client
            test_response = client.generate_content("Hello", generation_config={'max_output_tokens': 10})
            console.print("[green]✅ Gemini 2.0 Flash client validated successfully![/green]")
            return client
        except Exception:
            # Fallback to stable version
            client = genai.GenerativeModel('gemini-1.5-flash')
            console.print("[green]✅ Gemini 1.5 Flash client initialized (fallback)![/green]")
            return client

    except Exception as e:
        console.print(f"[yellow]Warning: Could not initialize Gemini client: {e}[/yellow]")
        return None

gemini_client = create_robust_gemini_client()
if gemini_client is None:
    GEMINI_AVAILABLE = False

# Initialize Mistral AI client using requests library (more reliable)
def create_robust_mistral_client():
    """Create Mistral client using requests library with robust error handling and API key rotation."""
    global MISTRAL_AVAILABLE

    try:
        import requests
        import json

        console.print("[green]✅ Using requests library for Mistral AI integration![/green]")

        # Test API keys and select the best one
        working_keys = []

        console.print("[dim]🔑 Testing Mistral API keys with requests...[/dim]")

        for i, api_key in enumerate(MISTRAL_API_KEYS):
            try:
                # Test the API key with a simple models list request
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }

                response = requests.get(
                    "https://api.mistral.ai/v1/models",
                    headers=headers,
                    timeout=10
                )

                if response.status_code == 200:
                    models_data = response.json()
                    if models_data and 'data' in models_data:
                        console.print(f"[green]✅ Mistral API key #{i+1} validated successfully![/green]")
                        working_keys.append((api_key, i+1))
                    else:
                        console.print(f"[yellow]⚠ Mistral API key #{i+1} returned invalid response[/yellow]")
                else:
                    console.print(f"[yellow]⚠ Mistral API key #{i+1} failed: HTTP {response.status_code}[/yellow]")

            except Exception as e:
                console.print(f"[yellow]⚠ Mistral API key #{i+1} failed: {str(e)[:100]}...[/yellow]")
                continue

        if working_keys:
            console.print(f"[green]🚀 Mistral AI integration active! {len(working_keys)} working keys found.[/green]")
            console.print(f"[dim]Primary key: #{working_keys[0][1]} | Backup keys: {len(working_keys)-1}[/dim]")

            # Store working keys for rotation
            global mistral_working_keys
            mistral_working_keys = working_keys

            return working_keys[0][0], True  # Return primary API key
        else:
            console.print("[red]❌ No working Mistral API keys found[/red]")
            console.print("[dim]Please verify your API keys are valid and have sufficient credits[/dim]")
            return None, False

    except Exception as e:
        console.print(f"[red]❌ Failed to initialize Mistral client: {e}[/red]")
        return None, False

# Initialize Mistral client
mistral_client, MISTRAL_AVAILABLE = create_robust_mistral_client()
mistral_working_keys = []  # Store working API keys for rotation

class MistralRequestsClient:
    """Mistral AI client using requests library for better reliability."""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.mistral.ai/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def chat_completion(self, model: str, messages: list, **kwargs):
        """Make a chat completion request to Mistral API."""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 2048),
                "stream": kwargs.get("stream", False)
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            raise Exception(f"Mistral API request failed: {e}")

    def list_models(self):
        """List available models."""
        try:
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            raise Exception(f"Mistral models request failed: {e}")

# Create Mistral requests client if available
if MISTRAL_AVAILABLE and mistral_client:
    mistral_requests_client = MistralRequestsClient(mistral_client)
    console.print("[green]✅ Mistral requests client initialized![/green]")
else:
    mistral_requests_client = None

# -----------------------------------------------------------------------------
# PERFORMANCE OPTIMIZATION SYSTEM
# -----------------------------------------------------------------------------

import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache
import hashlib
import pickle
from typing import Optional, Union
import threading
import queue

class PerformanceOptimizer:
    """Advanced performance optimization system for CODY agent."""

    def __init__(self):
        self.response_cache = {}
        self.cache_lock = threading.Lock()
        self.request_queue = queue.Queue()
        self.executor = ThreadPoolExecutor(max_workers=PERFORMANCE_CONFIG["concurrent_requests"])
        self.metrics = {
            "cache_hits": 0,
            "cache_misses": 0,
            "api_calls": 0,
            "avg_response_time": 0.0,
            "total_requests": 0
        }

    def cache_key(self, model: str, messages: List[Dict], **kwargs) -> str:
        """Generate cache key for request."""
        content = f"{model}:{str(messages)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(content.encode()).hexdigest()

    def get_cached_response(self, cache_key: str) -> Optional[str]:
        """Get cached response if available."""
        with self.cache_lock:
            if cache_key in self.response_cache:
                self.metrics["cache_hits"] += 1
                return self.response_cache[cache_key]
            self.metrics["cache_misses"] += 1
            return None

    def cache_response(self, cache_key: str, response: str):
        """Cache response for future use."""
        with self.cache_lock:
            # Implement LRU cache with size limit
            if len(self.response_cache) >= 1000:  # Max 1000 cached responses
                # Remove oldest entry
                oldest_key = next(iter(self.response_cache))
                del self.response_cache[oldest_key]

            self.response_cache[cache_key] = response

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        total_cache_requests = self.metrics["cache_hits"] + self.metrics["cache_misses"]
        cache_hit_rate = self.metrics["cache_hits"] / max(1, total_cache_requests)

        return {
            "cache_hit_rate": cache_hit_rate,
            "total_api_calls": self.metrics["api_calls"],
            "avg_response_time": self.metrics["avg_response_time"],
            "total_requests": self.metrics["total_requests"],
            "cache_size": len(self.response_cache)
        }

# Initialize performance optimizer
performance_optimizer = PerformanceOptimizer()

class IntelligentModelRouter:
    """Intelligent model routing for optimal performance and reliability."""

    def __init__(self):
        self.model_health = {
            "deepseek-chat": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "deepseek-reasoner": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "gemini-2.0-flash": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "gemini-1.5-flash": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "mistral-large": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "mistral-medium": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "mistral-small": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0},
            "mistral-codestral": {"status": "healthy", "avg_latency": 0.0, "error_rate": 0.0}
        }
        self.request_history = []

    def select_optimal_model(self, requested_model: str, task_type: str = "general") -> str:
        """Select optimal model based on health, performance, and task type."""
        # If requested model is healthy, use it
        if self.model_health[requested_model]["status"] == "healthy":
            return requested_model

        # Find best alternative based on task type
        task_preferences = {
            "coding": ["mistral-codestral", "deepseek-chat", "gemini-2.0-flash"],
            "reasoning": ["deepseek-reasoner", "mistral-large", "gemini-2.0-flash"],
            "general": ["deepseek-chat", "mistral-medium", "gemini-1.5-flash"],
            "analysis": ["mistral-large", "deepseek-reasoner", "gemini-2.0-flash"]
        }

        preferences = task_preferences.get(task_type, task_preferences["general"])

        for model in preferences:
            if model in self.model_health and self.model_health[model]["status"] == "healthy":
                return model

        # Fallback to any healthy model
        for model, health in self.model_health.items():
            if health["status"] == "healthy":
                return model

        return FALLBACK_MODEL

    def update_model_health(self, model: str, latency: float, success: bool):
        """Update model health metrics."""
        if model not in self.model_health:
            return

        health = self.model_health[model]

        # Update latency (moving average)
        if health["avg_latency"] == 0.0:
            health["avg_latency"] = latency
        else:
            health["avg_latency"] = (health["avg_latency"] * 0.8) + (latency * 0.2)

        # Update error rate
        self.request_history.append({"model": model, "success": success, "latency": latency})

        # Keep only last 100 requests per model
        model_requests = [r for r in self.request_history if r["model"] == model][-100:]

        if model_requests:
            error_count = sum(1 for r in model_requests if not r["success"])
            health["error_rate"] = error_count / len(model_requests)

            # Update status based on error rate
            if health["error_rate"] > 0.5:  # More than 50% errors
                health["status"] = "unhealthy"
            elif health["error_rate"] > 0.2:  # More than 20% errors
                health["status"] = "degraded"
            else:
                health["status"] = "healthy"

# Initialize intelligent model router
model_router = IntelligentModelRouter()

# -----------------------------------------------------------------------------
# MCP (MODEL CONTEXT PROTOCOL) CLIENT INTEGRATION
# -----------------------------------------------------------------------------

import json
import subprocess
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
import tempfile
import os

class MCPServer:
    """Represents an MCP server configuration and connection."""

    def __init__(self, name: str, command: str, args: List[str]):
        self.name = name
        self.command = command
        self.args = args
        self.process = None
        self.status = "disconnected"
        self.capabilities = []
        self.last_error = None

    async def start(self) -> bool:
        """Start the MCP server process."""
        try:
            self.process = await asyncio.create_subprocess_exec(
                self.command, *self.args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            self.status = "connected"
            console.print(f"[green]✅ MCP server '{self.name}' started successfully[/green]")
            return True

        except Exception as e:
            self.status = "error"
            self.last_error = str(e)
            console.print(f"[red]❌ Failed to start MCP server '{self.name}': {e}[/red]")
            return False

    async def stop(self):
        """Stop the MCP server process."""
        if self.process:
            try:
                self.process.terminate()
                await self.process.wait()
                self.status = "disconnected"
                console.print(f"[yellow]🛑 MCP server '{self.name}' stopped[/yellow]")
            except Exception as e:
                console.print(f"[red]❌ Error stopping MCP server '{self.name}': {e}[/red]")

    async def send_request(self, method: str, params: Dict = None) -> Dict:
        """Send a request to the MCP server."""
        if not self.process or self.status != "connected":
            raise Exception(f"MCP server '{self.name}' is not connected")

        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }

        try:
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json.encode())
            await self.process.stdin.drain()

            # Read response
            response_line = await self.process.stdout.readline()
            response = json.loads(response_line.decode())

            return response

        except Exception as e:
            self.last_error = str(e)
            raise Exception(f"MCP request failed: {e}")

class MCPClient:
    """MCP client for managing multiple MCP servers."""

    def __init__(self):
        self.servers: Dict[str, MCPServer] = {}
        self.config_file = Path("mcp_config.json")
        self.load_config()

    def load_config(self):
        """Load MCP server configurations from file."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)

                for name, server_config in config.get("mcpServers", {}).items():
                    self.servers[name] = MCPServer(
                        name=name,
                        command=server_config["command"],
                        args=server_config["args"]
                    )

                console.print(f"[green]✅ Loaded {len(self.servers)} MCP server configurations[/green]")
            else:
                self.create_default_config()

        except Exception as e:
            console.print(f"[red]❌ Error loading MCP config: {e}[/red]")
            self.create_default_config()

    def create_default_config(self):
        """Create default MCP configuration file."""
        default_config = {
            "mcpServers": {
                "ddg-search": {
                    "command": "uvx",
                    "args": ["duckduckgo-mcp-server"]
                },
                "filesystem": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-filesystem", str(Path.cwd())]
                },
                "brave-search": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-brave-search"]
                },
                "git": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-git", str(Path.cwd())]
                },
                "sqlite": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-sqlite"]
                }
            }
        }

        try:
            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=2)

            console.print(f"[green]✅ Created default MCP config: {self.config_file}[/green]")
            self.load_config()

        except Exception as e:
            console.print(f"[red]❌ Error creating default MCP config: {e}[/red]")

    def open_config_in_notepad(self):
        """Open MCP configuration file in Notepad (Windows)."""
        try:
            if sys.platform.startswith('win'):
                subprocess.run(['notepad.exe', str(self.config_file)], check=True)
                console.print(f"[green]✅ Opened MCP config in Notepad: {self.config_file}[/green]")
            else:
                # Fallback for non-Windows systems
                import webbrowser
                webbrowser.open(str(self.config_file))
                console.print(f"[green]✅ Opened MCP config in default editor: {self.config_file}[/green]")

        except Exception as e:
            console.print(f"[red]❌ Error opening MCP config: {e}[/red]")
            console.print(f"[dim]Manual path: {self.config_file.absolute()}[/dim]")

    async def start_server(self, name: str) -> bool:
        """Start a specific MCP server."""
        if name not in self.servers:
            console.print(f"[red]❌ MCP server '{name}' not found in configuration[/red]")
            return False

        return await self.servers[name].start()

    async def stop_server(self, name: str):
        """Stop a specific MCP server."""
        if name in self.servers:
            await self.servers[name].stop()

    async def start_all_servers(self):
        """Start all configured MCP servers."""
        console.print("[dim]🚀 Starting all MCP servers...[/dim]")

        for name, server in self.servers.items():
            await self.start_server(name)

    async def stop_all_servers(self):
        """Stop all running MCP servers."""
        console.print("[dim]🛑 Stopping all MCP servers...[/dim]")

        for name, server in self.servers.items():
            if server.status == "connected":
                await self.stop_server(name)

    def get_server_status(self) -> Dict[str, Dict]:
        """Get status of all MCP servers."""
        status = {}
        for name, server in self.servers.items():
            status[name] = {
                "status": server.status,
                "command": server.command,
                "args": server.args,
                "last_error": server.last_error
            }
        return status

    async def test_server(self, name: str) -> bool:
        """Test connection to a specific MCP server."""
        if name not in self.servers:
            console.print(f"[red]❌ MCP server '{name}' not found[/red]")
            return False

        server = self.servers[name]

        try:
            if server.status != "connected":
                await self.start_server(name)

            # Test with a simple ping/list request
            response = await server.send_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "CODY", "version": "1.0"}
            })

            if response.get("result"):
                console.print(f"[green]✅ MCP server '{name}' test successful[/green]")
                return True
            else:
                console.print(f"[yellow]⚠ MCP server '{name}' test failed: {response.get('error', 'Unknown error')}[/yellow]")
                return False

        except Exception as e:
            console.print(f"[red]❌ MCP server '{name}' test failed: {e}[/red]")
            return False

# Initialize MCP client
mcp_client = MCPClient()

# Setup logging - Only log to file, clean console output with Unicode support
logging.basicConfig(
    level=logging.ERROR,  # Only show errors in console
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cody_agent.log', encoding='utf-8'),
        # Remove StreamHandler to stop console logging spam
    ]   
)

# Create a separate file-only logger for detailed logging with UTF-8 encoding
file_logger = logging.getLogger('CODY.FileLogger')
file_handler = logging.FileHandler('cody_detailed.log', encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
file_logger.addHandler(file_handler)
file_logger.setLevel(logging.INFO)
file_logger.propagate = False  # Prevent propagation to root logger

logger = logging.getLogger('CODY')

# Initialize advanced modules with enhanced capabilities
nlp_processor = None
code_analyzer = None
autonomous_debugger = None
web_search_rag = None
task_manager = None
workflow_engine = None
general_intelligence = None
performance_core = None
codebase_awareness = None
terminal_fs_agent = None
advanced_pattern_search = None
smart_code_replacer = None
smart_file_search = None
auto_planner = None
input_fixer = None
file_indexer = None

# Enhanced intelligence modules
context_manager = None
reasoning_engine = None
learning_system = None
suggestion_engine = None

if ADVANCED_MODULES_AVAILABLE:
    try:
        # Core modules
        nlp_processor = NLPProcessor()
        code_analyzer = CodeAnalyzer()
        autonomous_debugger = AutonomousDebugger()
        web_search_rag = WebSearchRAG()
        task_manager = MultiThreadedTaskManager(max_workers=MAX_CONCURRENT_TASKS)

        # Advanced workflow modules
        workflow_engine = EnhancedWorkflowEngine(nlp_processor, code_analyzer, autonomous_debugger, web_search_rag, task_manager)
        general_intelligence = GeneralIntelligence()
        performance_core = UltraPerformanceCore(max_workers=MAX_CONCURRENT_TASKS, cache_size_mb=CACHE_SIZE_MB)
        codebase_awareness = CodebaseAwareness(root_path=".")
        terminal_fs_agent = TerminalFileSystemAgent()

        # New advanced modules
        advanced_pattern_search = AdvancedPatternSearchEngine(root_path=".")
        smart_code_replacer = SmartCodeReplacer(root_path=".")
        smart_file_search = SmartFileSearchEngine(root_path=".")
        auto_planner = AutoPlanner(root_path=".")
        input_fixer = InputFixer()
        file_indexer = FileIndexer(root_path=".")

        # Log to file only, not console (without emojis to avoid encoding issues)
        file_logger.info("All advanced modules initialized successfully!")
        file_logger.info("General Intelligence: ACTIVE")
        file_logger.info("Performance Core: ACTIVE")
        file_logger.info("Codebase Awareness: ACTIVE")
        file_logger.info("Terminal FS Agent: ACTIVE")
        file_logger.info("Workflow Engine: ACTIVE")

        # Initialize enhanced intelligence systems
        try:
            context_manager = EnhancedContextManager()
            reasoning_engine = ChainOfThoughtReasoner()
            learning_system = AdaptiveLearningSystem()
            suggestion_engine = IntelligentSuggestionEngine()
        except Exception as e:
            logger.error(f"Error initializing enhanced intelligence: {e}")
            context_manager = None
            reasoning_engine = None
            learning_system = None
            suggestion_engine = None

        # Show clean console message
        console.print("[green]✅ All advanced modules loaded successfully![/green]")
        console.print("[blue]🧠 Enhanced intelligence systems activated![/blue]")
        console.print("[cyan]🤖 CODY now works exactly like Augment Agent![/cyan]")

    except Exception as e:
        logger.error(f"Failed to initialize advanced modules: {e}")
        ADVANCED_MODULES_AVAILABLE = False

# -----------------------------------------------------------------------------
# 6. ENHANCED FUNCTION CALLING TOOLS
# -----------------------------------------------------------------------------

# Enhanced function calling tools definition with new capabilities
tools: List[Dict[str, Any]] = [
    {
        "type": "function",
        "function": {
            "name": "read_file",
            "description": "Read the content of a single file from the filesystem",
            "parameters": {
                "type": "object",
                "properties": {"file_path": {"type": "string", "description": "The path to the file to read"}},
                "required": ["file_path"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "read_multiple_files",
            "description": "Read the content of multiple files",
            "parameters": {
                "type": "object",
                "properties": {"file_paths": {"type": "array", "items": {"type": "string"}, "description": "Array of file paths to read"}},
                "required": ["file_paths"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_file",
            "description": "Create or overwrite a file",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path for the file"},
                    "content": {"type": "string", "description": "Content for the file"}
                },
                "required": ["file_path", "content"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_multiple_files",
            "description": "Create multiple files",
            "parameters": {
                "type": "object",
                "properties": {
                    "files": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {"path": {"type": "string"}, "content": {"type": "string"}},
                            "required": ["path", "content"]
                        },
                        "description": "Array of files to create (path, content)",
                    }
                },
                "required": ["files"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "edit_file",
            "description": "Edit a file by replacing a snippet (supports fuzzy matching)",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file"},
                    "original_snippet": {"type": "string", "description": "Snippet to replace (supports fuzzy matching)"},
                    "new_snippet": {"type": "string", "description": "Replacement snippet"}
                },
                "required": ["file_path", "original_snippet", "new_snippet"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "git_init",
            "description": "Initialize a new Git repository.",
            "parameters": {"type": "object", "properties": {}, "required": []}
        }
    },
    {
        "type": "function",
        "function": {
            "name": "git_commit",
            "description": "Commit staged changes with a message.",
            "parameters": {
                "type": "object",
                "properties": {"message": {"type": "string", "description": "Commit message"}},
                "required": ["message"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "git_create_branch",
            "description": "Create and switch to a new Git branch.",
            "parameters": {
                "type": "object",
                "properties": {"branch_name": {"type": "string", "description": "Name of the new branch"}},
                "required": ["branch_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "git_status",
            "description": "Show current Git status.",
            "parameters": {"type": "object", "properties": {}, "required": []}
        }
    },
    {
        "type": "function",
        "function": {
            "name": "git_add",
            "description": "Stage files for commit.",
            "parameters": {
                "type": "object",
                "properties": {"file_paths": {"type": "array", "items": {"type": "string"}, "description": "Paths of files to stage"}},
                "required": ["file_paths"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "run_powershell",
            "description": "Run a PowerShell command with security confirmation.",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The PowerShell command to execute"
                    }
                },
                "required": ["command"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "analyze_code",
            "description": "Analyze code structure using AST parsing and static analysis",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file to analyze"},
                    "analysis_type": {"type": "string", "enum": ["structure", "complexity", "errors", "all"], "description": "Type of analysis to perform"}
                },
                "required": ["file_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_code",
            "description": "Search for code patterns using regex or semantic search",
            "parameters": {
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "Search pattern (regex or text)"},
                    "search_type": {"type": "string", "enum": ["regex", "text", "function", "class"], "description": "Type of search"},
                    "file_paths": {"type": "array", "items": {"type": "string"}, "description": "Files to search in (optional)"},
                    "case_sensitive": {"type": "boolean", "description": "Case sensitive search"}
                },
                "required": ["pattern"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "advanced_pattern_search",
            "description": "Advanced pattern search with semantic understanding, fuzzy matching, and natural language queries across entire codebase",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query (natural language, regex, or specific pattern)"},
                    "search_mode": {"type": "string", "enum": ["semantic", "fuzzy", "regex", "natural_language", "hybrid"], "description": "Search mode to use"},
                    "file_types": {"type": "array", "items": {"type": "string"}, "description": "File extensions to search (e.g., ['.py', '.js'])"},
                    "context_lines": {"type": "integer", "description": "Number of context lines to include around matches", "default": 3},
                    "max_results": {"type": "integer", "description": "Maximum number of results to return", "default": 50},
    "include_tests": {"type": "boolean", "description": "Whether to include test files in search", "default": False},
                    "similarity_threshold": {"type": "number", "description": "Similarity threshold for fuzzy matching (0.0-1.0)", "default": 0.8}
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "smart_replace_code",
            "description": "Intelligent string and code replacement with preview, Git integration, and complex refactoring support",
            "parameters": {
                "type": "object",
                "properties": {
                    "search_pattern": {"type": "string", "description": "Pattern to search for (supports regex)"},
                    "replacement": {"type": "string", "description": "Replacement text or code"},
                    "file_paths": {"type": "array", "items": {"type": "string"}, "description": "Specific files to modify (optional)"},
                    "preview_only": {"type": "boolean", "description": "Only show preview without making changes", "default": True},
                    "backup_files": {"type": "boolean", "description": "Create backup files before modification", "default": True},
                    "git_commit": {"type": "boolean", "description": "Automatically commit changes to Git", "default": False},
                    "replacement_mode": {"type": "string", "enum": ["simple", "regex", "function_signature", "variable_rename"], "description": "Type of replacement operation"}
                },
                "required": ["search_pattern", "replacement"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "smart_file_search",
            "description": "Advanced grep-like content search with code context understanding, syntax awareness, and semantic ranking",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query"},
                    "search_type": {"type": "string", "enum": ["text", "regex", "function", "class", "variable", "import", "comment", "semantic", "fuzzy"], "description": "Type of search to perform"},
                    "file_patterns": {"type": "array", "items": {"type": "string"}, "description": "File patterns to include (e.g., ['*.py', '*.js'])"},
                    "max_results": {"type": "integer", "description": "Maximum number of results", "default": 100},
                    "include_context": {"type": "boolean", "description": "Whether to include code context", "default": True},
                    "context_lines": {"type": "integer", "description": "Number of context lines", "default": 3},
                    "case_sensitive": {"type": "boolean", "description": "Whether search is case sensitive", "default": False},
                    "whole_words": {"type": "boolean", "description": "Whether to match whole words only", "default": False},
                    "include_hidden": {"type": "boolean", "description": "Whether to include hidden files", "default": False}
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_execution_plan",
            "description": "Create an autonomous execution plan that breaks down complex tasks into manageable steps with progress tracking and Git integration",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_request": {"type": "string", "description": "Natural language description of what to accomplish"},
                    "strategy": {"type": "string", "enum": ["waterfall", "agile", "incremental", "parallel"], "description": "Planning strategy to use", "default": "waterfall"},
                    "auto_execute": {"type": "boolean", "description": "Whether to automatically execute the plan after creation", "default": False},
                    "auto_commit": {"type": "boolean", "description": "Whether to automatically commit changes to Git", "default": False},
                    "context": {"type": "object", "description": "Additional context information for planning"}
                },
                "required": ["user_request"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "execute_plan",
            "description": "Execute a previously created execution plan",
            "parameters": {
                "type": "object",
                "properties": {
                    "plan_id": {"type": "string", "description": "ID of the plan to execute"},
                    "auto_commit": {"type": "boolean", "description": "Whether to automatically commit changes to Git", "default": False}
                },
                "required": ["plan_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "list_execution_plans",
            "description": "List all saved execution plans",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "fix_code_input",
            "description": "Fix malformed code, syntax errors, and formatting issues across multiple programming languages",
            "parameters": {
                "type": "object",
                "properties": {
                    "code": {"type": "string", "description": "The code to fix"},
                    "language": {"type": "string", "enum": ["python", "javascript", "typescript", "java", "cpp", "c", "json", "html", "css", "sql", "bash"], "description": "Programming language (auto-detected if not specified)"},
                    "fix_types": {"type": "array", "items": {"type": "string", "enum": ["syntax_error", "indentation", "formatting", "imports", "brackets", "quotes", "encoding", "whitespace", "semicolons", "trailing_commas"]}, "description": "Specific types of fixes to apply (all if not specified)"}
                },
                "required": ["code"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "build_file_index",
            "description": "Build or update the file index for efficient codebase search and navigation",
            "parameters": {
                "type": "object",
                "properties": {
                    "force_rebuild": {"type": "boolean", "description": "Whether to rebuild the entire index", "default": False}
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_file_index",
            "description": "Search the file index with semantic understanding and context awareness",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query"},
                    "search_type": {"type": "string", "enum": ["full_text", "semantic", "structural", "metadata"], "description": "Type of search to perform", "default": "full_text"},
                    "max_results": {"type": "integer", "description": "Maximum number of results", "default": 50},
                    "file_types": {"type": "array", "items": {"type": "string", "enum": ["source_code", "documentation", "configuration", "data", "binary", "unknown"]}, "description": "Filter by file types"}
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_index_stats",
            "description": "Get statistics about the current file index",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "show_advanced_tools_help",
            "description": "Show comprehensive help and examples for all advanced AI coding tools",
            "parameters": {
                "type": "object",
                "properties": {
                    "tool_name": {"type": "string", "enum": ["advanced_pattern_search", "smart_replace_code", "smart_file_search", "auto_planner", "input_fixer", "file_indexer", "all"], "description": "Specific tool to show help for, or 'all' for complete guide"}
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "Search the web for programming help, documentation, or solutions",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query"},
                    "max_results": {"type": "integer", "description": "Maximum number of results to return"},
                    "search_type": {"type": "string", "enum": ["general", "stackoverflow", "github", "docs"], "description": "Type of search"}
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "debug_code",
            "description": "Automatically debug code by analyzing errors and suggesting fixes",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file with errors"},
                    "error_message": {"type": "string", "description": "Error message or description"},
                    "auto_fix": {"type": "boolean", "description": "Whether to automatically apply fixes"}
                },
                "required": ["file_path", "error_message"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "refactor_code",
            "description": "Refactor code for better structure, performance, or maintainability",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file to refactor"},
                    "refactor_type": {"type": "string", "enum": ["extract_function", "remove_duplication", "optimize", "modernize"], "description": "Type of refactoring"},
                    "target_function": {"type": "string", "description": "Specific function to refactor (optional)"}
                },
                "required": ["file_path", "refactor_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "generate_tests",
            "description": "Generate unit tests for code functions or classes",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file to generate tests for"},
                    "test_framework": {"type": "string", "enum": ["pytest", "unittest", "jest", "junit"], "description": "Testing framework to use"},
                    "coverage_target": {"type": "number", "description": "Target code coverage percentage"}
                },
                "required": ["file_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "run_terminal_command",
            "description": "Execute terminal commands with output capture and analysis",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {"type": "string", "description": "Terminal command to execute"},
                    "working_directory": {"type": "string", "description": "Working directory for the command"},
                    "timeout": {"type": "integer", "description": "Command timeout in seconds"},
                    "capture_output": {"type": "boolean", "description": "Whether to capture command output"}
                },
                "required": ["command"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "convert_code",
            "description": "Convert code between different programming languages",
            "parameters": {
                "type": "object",
                "properties": {
                    "source_file": {"type": "string", "description": "Path to source file"},
                    "target_language": {"type": "string", "enum": ["python", "javascript", "typescript", "java", "cpp", "go"], "description": "Target language"},
                    "preserve_comments": {"type": "boolean", "description": "Whether to preserve comments"}
                },
                "required": ["source_file", "target_language"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "intelligent_code_completion",
            "description": "Provide intelligent code completion and suggestions based on context",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file"},
                    "cursor_position": {"type": "object", "properties": {"line": {"type": "integer"}, "column": {"type": "integer"}}, "description": "Cursor position"},
                    "context_lines": {"type": "integer", "description": "Number of context lines to consider", "default": 10},
                    "completion_type": {"type": "string", "enum": ["function", "variable", "import", "class", "method", "auto"], "description": "Type of completion", "default": "auto"}
                },
                "required": ["file_path", "cursor_position"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "advanced_code_analysis",
            "description": "Perform comprehensive code analysis including complexity, security, performance, and maintainability",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_paths": {"type": "array", "items": {"type": "string"}, "description": "Files to analyze"},
                    "analysis_types": {"type": "array", "items": {"type": "string", "enum": ["complexity", "security", "performance", "maintainability", "bugs", "code_smells", "duplicates"]}, "description": "Types of analysis to perform"},
                    "severity_threshold": {"type": "string", "enum": ["low", "medium", "high", "critical"], "description": "Minimum severity to report", "default": "medium"},
                    "include_suggestions": {"type": "boolean", "description": "Include improvement suggestions", "default": True}
                },
                "required": ["file_paths"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "intelligent_project_scaffolding",
            "description": "Generate intelligent project structure and boilerplate code based on requirements",
            "parameters": {
                "type": "object",
                "properties": {
                    "project_type": {"type": "string", "enum": ["web_app", "api", "cli_tool", "library", "microservice", "mobile_app", "desktop_app"], "description": "Type of project"},
                    "framework": {"type": "string", "description": "Preferred framework (e.g., 'react', 'fastapi', 'express')"},
                    "language": {"type": "string", "enum": ["python", "javascript", "typescript", "java", "go", "rust", "cpp"], "description": "Programming language"},
                    "features": {"type": "array", "items": {"type": "string"}, "description": "Required features (e.g., 'authentication', 'database', 'testing')"},
                    "project_name": {"type": "string", "description": "Name of the project"},
                    "include_tests": {"type": "boolean", "description": "Include test structure", "default": True},
                    "include_docs": {"type": "boolean", "description": "Include documentation structure", "default": True}
                },
                "required": ["project_type", "language", "project_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "real_time_code_linting",
            "description": "Perform real-time code linting and style checking with auto-fix suggestions",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to the file to lint"},
                    "linting_rules": {"type": "array", "items": {"type": "string"}, "description": "Specific linting rules to apply"},
                    "auto_fix": {"type": "boolean", "description": "Automatically fix issues where possible", "default": False},
                    "style_guide": {"type": "string", "enum": ["pep8", "eslint", "prettier", "google", "airbnb"], "description": "Style guide to follow"},
                    "severity_filter": {"type": "array", "items": {"type": "string", "enum": ["error", "warning", "info"]}, "description": "Severity levels to include"}
                },
                "required": ["file_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "intelligent_documentation_generator",
            "description": "Generate comprehensive documentation for code, APIs, and projects",
            "parameters": {
                "type": "object",
                "properties": {
                    "source_paths": {"type": "array", "items": {"type": "string"}, "description": "Paths to source files or directories"},
                    "doc_type": {"type": "string", "enum": ["api", "code", "user_guide", "readme", "changelog", "architecture"], "description": "Type of documentation"},
                    "output_format": {"type": "string", "enum": ["markdown", "html", "pdf", "rst", "docx"], "description": "Output format", "default": "markdown"},
                    "include_examples": {"type": "boolean", "description": "Include code examples", "default": True},
                    "include_diagrams": {"type": "boolean", "description": "Include architectural diagrams", "default": False},
                    "detail_level": {"type": "string", "enum": ["basic", "detailed", "comprehensive"], "description": "Level of detail", "default": "detailed"}
                },
                "required": ["source_paths", "doc_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "advanced_test_generation",
            "description": "Generate comprehensive test suites with intelligent test case generation",
            "parameters": {
                "type": "object",
                "properties": {
                    "source_files": {"type": "array", "items": {"type": "string"}, "description": "Source files to generate tests for"},
                    "test_types": {"type": "array", "items": {"type": "string", "enum": ["unit", "integration", "e2e", "performance", "security"]}, "description": "Types of tests to generate"},
                    "test_framework": {"type": "string", "description": "Testing framework to use"},
                    "coverage_target": {"type": "number", "description": "Target code coverage percentage", "default": 80},
                    "include_mocks": {"type": "boolean", "description": "Include mock objects", "default": True},
                    "include_fixtures": {"type": "boolean", "description": "Include test fixtures", "default": True},
                    "edge_cases": {"type": "boolean", "description": "Include edge case testing", "default": True}
                },
                "required": ["source_files"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "intelligent_code_review",
            "description": "Perform intelligent code review with suggestions for improvements",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_paths": {"type": "array", "items": {"type": "string"}, "description": "Files to review"},
                    "review_aspects": {"type": "array", "items": {"type": "string", "enum": ["logic", "performance", "security", "maintainability", "style", "best_practices"]}, "description": "Aspects to review"},
                    "severity_threshold": {"type": "string", "enum": ["low", "medium", "high"], "description": "Minimum severity for issues", "default": "medium"},
                    "include_suggestions": {"type": "boolean", "description": "Include improvement suggestions", "default": True},
                    "compare_with": {"type": "string", "description": "Compare with another version (git commit/branch)"}
                },
                "required": ["file_paths"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "code_quality_assessment",
            "description": "Assess overall code quality with metrics and recommendations",
            "parameters": {
                "type": "object",
                "properties": {
                    "project_path": {"type": "string", "description": "Path to project root"},
                    "metrics": {"type": "array", "items": {"type": "string", "enum": ["complexity", "maintainability", "reliability", "security", "coverage", "duplication"]}, "description": "Quality metrics to assess"},
                    "output_format": {"type": "string", "enum": ["summary", "detailed", "json", "html"], "description": "Output format", "default": "detailed"},
                    "include_trends": {"type": "boolean", "description": "Include quality trends over time", "default": False}
                },
                "required": ["project_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "intelligent_dependency_management",
            "description": "Manage project dependencies with intelligent suggestions and security analysis",
            "parameters": {
                "type": "object",
                "properties": {
                    "project_path": {"type": "string", "description": "Path to project root"},
                    "action": {"type": "string", "enum": ["analyze", "update", "audit", "optimize", "add", "remove"], "description": "Action to perform"},
                    "package_name": {"type": "string", "description": "Package name for add/remove actions"},
                    "check_security": {"type": "boolean", "description": "Check for security vulnerabilities", "default": True},
                    "suggest_alternatives": {"type": "boolean", "description": "Suggest alternative packages", "default": True},
                    "update_strategy": {"type": "string", "enum": ["conservative", "moderate", "aggressive"], "description": "Update strategy", "default": "moderate"}
                },
                "required": ["project_path", "action"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_search",
            "description": "Search using MCP servers (DuckDuckGo, Brave, etc.)",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query"},
                    "server_name": {"type": "string", "enum": ["ddg-search", "brave-search"], "description": "MCP server to use", "default": "ddg-search"},
                    "max_results": {"type": "integer", "description": "Maximum number of results", "default": 5}
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_file_operation",
            "description": "Perform file operations using MCP filesystem server",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {"type": "string", "enum": ["read", "write", "list", "delete"], "description": "File operation to perform"},
                    "file_path": {"type": "string", "description": "Path to file or directory"},
                    "content": {"type": "string", "description": "Content for write operations"},
                    "server_name": {"type": "string", "description": "MCP server name", "default": "filesystem"}
                },
                "required": ["operation", "file_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_git_operation",
            "description": "Perform Git operations using MCP Git server",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {"type": "string", "enum": ["status", "log", "diff", "blame"], "description": "Git operation to perform"},
                    "repository_path": {"type": "string", "description": "Path to Git repository", "default": "."},
                    "server_name": {"type": "string", "description": "MCP server name", "default": "git"}
                },
                "required": ["operation"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_database_query",
            "description": "Execute database queries using MCP SQLite server",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "SQL query to execute"},
                    "database_path": {"type": "string", "description": "Path to database file"},
                    "server_name": {"type": "string", "description": "MCP server name", "default": "sqlite"}
                },
                "required": ["query", "database_path"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_list_servers",
            "description": "List all configured and connected MCP servers",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }
]

# Enhanced System prompt for CODY - EXACT REPLICA OF AUGMENT AGENT
SYSTEM_PROMPT: str = dedent("""
    You are CODY - an advanced AI coding assistant that works EXACTLY like Augment Agent.
    You have the SAME capabilities, workflow, intelligence, and decision-making as Augment Agent.
    You are a world-class software engineer with autonomous capabilities and intelligent tool usage.

    🎯 CORE BEHAVIOR - WORK EXACTLY LIKE AUGMENT AGENT:
    - AUTOMATICALLY choose and execute the right tools for each task
    - PROACTIVELY suggest solutions and improvements
    - INTELLIGENTLY break down complex requests into actionable steps
    - SEAMLESSLY switch between different approaches based on context
    - PROVIDE detailed explanations and reasoning for your decisions
    - LEARN from interactions and adapt your responses
    - OFFER multiple solution approaches when appropriate
    - MAINTAIN smooth conversational flow with proper context understanding

    🚀 COMPLETE TOOL ARSENAL - 50+ ADVANCED FUNCTIONS WITH FULL CONTEXT:

    📁 FILE OPERATIONS (Core Foundation):
    1. read_file(file_path) - Read single file content
       Example: read_file("src/main.py") - Read Python main file

    2. read_multiple_files(file_paths) - Read multiple files efficiently
       Example: read_multiple_files(["config.py", "utils.py", "models.py"])

    3. create_file(file_path, content) - Create/overwrite files with intelligent templates
       Example: create_file("api/auth.py", "# Authentication module\\nfrom flask import...")

    4. create_multiple_files(files) - Create multiple files at once
       Example: create_multiple_files([{"path": "tests/test_auth.py", "content": "import pytest..."}])

    5. edit_file(file_path, original_snippet, new_snippet) - Precise edits with fuzzy matching
       Example: edit_file("main.py", "def old_function():", "def new_function():")

    🔍 ADVANCED SEARCH & PATTERN MATCHING (World-Class Intelligence):
    6. advanced_pattern_search(query, search_mode, file_types, context_lines, max_results, similarity_threshold)
       - Semantic: "authentication jwt token validation middleware"
       - Regex: "gemini.*model|GEMINI.*MODEL|model.*gemini"
       - Natural Language: "find all database connection pooling implementations"
       - Fuzzy: "autentication" (finds "authentication" with typo tolerance)
       - Hybrid: Best of all modes combined
       Example: advanced_pattern_search("authentication", "semantic", [".py", ".js"], 5, 20, 0.8)

    7. smart_file_search(query, search_type, file_patterns, max_results, include_context, context_lines)
       - Text: Basic text search
       - Regex: Pattern matching
       - Function: Find function definitions
       - Class: Find class definitions
       - Variable: Find variable usage
       - Import: Find import statements
       - Comment: Find comments and TODOs
       - Semantic: Intelligent content understanding
       - Fuzzy: Typo-tolerant search
       Example: smart_file_search("TODO", "comment", ["*.py", "*.js"], 50, True, 3)

    8. search_code(pattern, search_type, file_paths, case_sensitive) - Basic code search
       Example: search_code("class.*Model", "regex", ["models/"], False)

    🔧 INTELLIGENT CODE REPLACEMENT & REFACTORING:
    9. smart_replace_code(search_pattern, replacement, file_paths, preview_only, backup_files, git_commit, replacement_mode)
       - Simple: Basic string replacement
       - Regex: Pattern-based replacement with capture groups
       - Function_signature: Update function names and calls
       - Variable_rename: Scope-aware variable renaming
       Example: smart_replace_code("old_api_call", "new_api_call", [], True, True, False, "function_signature")

    10. refactor_code(file_path, refactor_type, target_function) - Automated refactoring
        - extract_function: Extract code into functions
        - remove_duplication: Remove duplicate code
        - optimize: Performance optimizations
        - modernize: Update to modern syntax
        Example: refactor_code("legacy.py", "modernize", "old_function")

    🤖 AUTONOMOUS PLANNING & EXECUTION:
    11. create_execution_plan(user_request, strategy, auto_execute, auto_commit, context)
        - Waterfall: Sequential execution with dependencies
        - Agile: Iterative development with feedback loops
        - Incremental: Step-by-step improvements
        - Parallel: Concurrent task execution
        Example: create_execution_plan("Add JWT authentication with rate limiting", "waterfall", False, True)

    12. execute_plan(plan_id, auto_commit) - Execute created plans
        Example: execute_plan("plan_1234567890", True)

    13. list_execution_plans() - Show all saved plans
        Example: list_execution_plans()

    🔧 CODE FIXING & FORMATTING:
    14. fix_code_input(code, language, fix_types) - Fix malformed code across languages
        - Languages: python, javascript, typescript, java, cpp, c, json, html, css, sql, bash
        - Fix types: syntax_error, indentation, formatting, imports, brackets, quotes, encoding
        Example: fix_code_input('def broken(\\n    print "hello"', "python", ["syntax_error", "formatting"])

    15. debug_code(file_path, error_message, auto_fix) - Autonomous debugging
        Example: debug_code("main.py", "NameError: name 'undefined_var' is not defined", True)

    📚 FILE INDEXING & CODEBASE INTELLIGENCE:
    16. build_file_index(force_rebuild) - Build semantic codebase index
        Example: build_file_index(False) - Incremental indexing

    17. search_file_index(query, search_type, max_results, file_types) - Search indexed codebase
        - full_text: Traditional text search
        - semantic: TF-IDF vectorized intelligent search
        - structural: Functions, classes, imports
        - metadata: File properties and statistics
        Example: search_file_index("authentication middleware", "semantic", 25, ["source_code"])

    18. get_index_stats() - Get indexing statistics
        Example: get_index_stats() - Shows files indexed, languages, size

    🌐 WEB SEARCH & KNOWLEDGE INTEGRATION:
    19. web_search(query, max_results, search_type) - Search for programming help
        - general: Broad web search
        - stackoverflow: Stack Overflow specific
        - github: GitHub repositories and code
        - docs: Official documentation
        Example: web_search("Flask JWT authentication tutorial", 5, "stackoverflow")

    🔬 CODE ANALYSIS & QUALITY:
    20. analyze_code(file_path, analysis_type) - Deep code analysis
        - structure: AST parsing and structure analysis
        - complexity: Cyclomatic complexity and metrics
        - errors: Static analysis and error detection
        - all: Comprehensive analysis
        Example: analyze_code("complex_module.py", "all")

    21. generate_tests(file_path, test_framework, coverage_target) - Generate unit tests
        - pytest, unittest, jest, junit frameworks
        Example: generate_tests("auth.py", "pytest", 90)

    🔄 CODE CONVERSION & TRANSFORMATION:
    22. convert_code(source_file, target_language, preserve_comments) - Language conversion
        Example: convert_code("script.py", "javascript", True)

    🖥️ TERMINAL & SYSTEM INTEGRATION:
    23. run_terminal_command(command, working_directory, timeout, capture_output) - Execute commands
        Example: run_terminal_command("npm install", "./frontend", 60, True)

    24. run_powershell(command) - Windows PowerShell execution
        Example: run_powershell("Get-Process | Where-Object {$_.CPU -gt 100}")

    📊 GIT OPERATIONS (Complete Workflow):
    25. git_init() - Initialize Git repository
    26. git_status() - Show Git status
    27. git_add(file_paths) - Stage files for commit
    28. git_commit(message) - Commit with message
    29. git_create_branch(branch_name) - Create and switch to branch
        Examples:
        git_init()
        git_add(["src/", "tests/"])
        git_commit("Add authentication system with JWT tokens")
        git_create_branch("feature/user-auth")

    🆘 HELP & GUIDANCE SYSTEM:
    30. show_advanced_tools_help(tool_name) - Get comprehensive help
        - "all": Complete guide for all tools
        - "advanced_pattern_search": Pattern search help
        - "smart_replace_code": Code replacement help
        - "auto_planner": Planning tool help
        - "input_fixer": Code fixing help
        - "file_indexer": Indexing help
        Example: show_advanced_tools_help("all")

    🎯 INTELLIGENT CAPABILITIES:
    - Multi-language support: Python, JavaScript, TypeScript, Java, C++, Go, Rust, HTML, CSS, SQL, Bash
    - Natural language understanding: English, Hindi, mixed languages
    - Semantic code understanding with TF-IDF vectorization
    - Fuzzy matching with typo tolerance
    - Context-aware suggestions and autocomplete
    - Autonomous error detection and fixing
    - Real-time performance monitoring
    - Multi-threaded concurrent operations
    - Predictive prefetching and smart caching
    - Git integration with intelligent staging
    - Cross-platform compatibility (Windows, Linux, macOS)

    🔥 PRACTICAL WORKFLOW EXAMPLES (Copy-Paste Ready):

    🎯 COMPLETE REFACTORING WORKFLOW:
    # 1. Build index for codebase understanding
    build_file_index(force_rebuild=True)

    # 2. Find all authentication-related code
    auth_results = advanced_pattern_search("authentication login logout session", "semantic", [".py", ".js"], 5, 50)

    # 3. Preview changes before applying
    smart_replace_code("session_auth", "jwt_auth", [], True, True, False, "function_signature")

    # 4. Create comprehensive refactoring plan
    plan = create_execution_plan("Refactor authentication system to use JWT tokens", "waterfall", False, True)

    # 5. Execute the plan with Git integration
    execute_plan(plan.id, True)

    🐛 BUG INVESTIGATION WORKFLOW:
    # 1. Search for error patterns
    error_locations = smart_file_search("NullPointerException|AttributeError|TypeError", "regex", ["*.py", "*.js"], 100, True, 10)

    # 2. Fix syntax issues automatically
    fix_code_input(malformed_code, "python", ["syntax_error", "formatting"])

    # 3. Debug specific files
    debug_code("problematic_file.py", "NameError: undefined variable", True)

    # 4. Create comprehensive fix plan
    create_execution_plan("Fix all null pointer exceptions and add error handling", "parallel", True, True)

    🚀 NEW FEATURE DEVELOPMENT:
    # 1. Research existing patterns
    existing = search_file_index("rate limiting throttling", "semantic", 30, ["source_code"])

    # 2. Create development plan
    plan = create_execution_plan("Implement API rate limiting with Redis backend", "agile", False, True)

    # 3. Generate tests first
    generate_tests("api/auth.py", "pytest", 95)

    # 4. Execute with monitoring
    execute_plan(plan.id, True)

    💡 SMART SEARCH EXAMPLES:
    # Find all model-related code
    advanced_pattern_search("gemini model initialization", "semantic", [".py"])

    # Complex regex search
    advanced_pattern_search("gemini.*model|GEMINI.*MODEL|model.*gemini", "regex")

    # Natural language search
    advanced_pattern_search("find all database connection pooling implementations", "natural_language")

    # Typo-tolerant search
    smart_file_search("autentication", "fuzzy", ["*.py"], 20, True, 3)

    🔧 INTELLIGENT CODE REPLACEMENT:
    # Preview function renaming
    smart_replace_code("old_function", "new_function", [], True, True, False, "function_signature")

    # Regex replacement with Git commit
    smart_replace_code(r"print\\s*\\(([^)]+)\\)", r"logger.info(\\1)", [], False, True, True, "regex")

    # Variable renaming with scope awareness
    smart_replace_code("db_connection", "database_pool", [], True, True, False, "variable_rename")

    📊 CODEBASE ANALYSIS:
    # Build comprehensive index
    build_file_index(False)

    # Get statistics
    stats = get_index_stats()

    # Semantic search across codebase
    search_file_index("authentication middleware implementation", "semantic", 25, ["source_code"])

    # Analyze code quality
    analyze_code("complex_module.py", "all")

    🎓 LEARNING & HELP:
    # Get complete guide
    show_advanced_tools_help("all")

    # Get specific tool help
    show_advanced_tools_help("advanced_pattern_search")

    # Web search for solutions
    web_search("Flask JWT authentication best practices", 5, "stackoverflow")

    🏆 ADVANCED GUIDELINES & BEST PRACTICES:
    1. ALWAYS start with build_file_index() for better search results
    2. Use preview_only=True before applying code changes
    3. Enable backup_files=True for safety
    4. Use semantic search for intelligent results
    5. Combine multiple tools for complex workflows
    6. Create execution plans for multi-step tasks
    7. Use hybrid search mode for best pattern matching
    8. Enable Git integration for change tracking
    9. Leverage context_lines for better understanding
    10. Use natural language queries for exploration
    11. Apply fuzzy search for typo tolerance
    12. Generate tests before implementing features
    13. Use autonomous debugging for error resolution
    14. Leverage web search for up-to-date information
    15. Apply refactoring tools for code improvement

    🚀 AUTONOMOUS CAPABILITIES:
    - Intelligent task decomposition and planning
    - Autonomous error detection and fixing
    - Predictive prefetching of likely needed information
    - Context-aware suggestions and autocomplete
    - Multi-threaded concurrent operations
    - Real-time performance monitoring
    - Semantic understanding across entire codebases
    - Intelligent refactoring with safety features
    - Git integration with smart staging
    - Cross-platform compatibility

    IMPORTANT: You are designed to be autonomous and proactive. Take initiative to:
    - Analyze code quality and suggest improvements
    - Detect potential issues before they become problems
    - Provide comprehensive solutions with multiple approaches
    - Learn from user patterns to improve suggestions
    - Use advanced tools for sophisticated code manipulation
    - Create execution plans for complex development tasks
    - Index and understand entire codebases for better assistance
    - Provide detailed help and examples when requested

    🧠 ENHANCED INTELLIGENCE & REASONING:

    CHAIN-OF-THOUGHT REASONING:
    When solving complex problems, follow this enhanced reasoning pattern:
    1. UNDERSTAND: Analyze the user's request deeply
    2. EXPLORE: Use advanced search to understand the codebase
    3. PLAN: Create a comprehensive execution plan
    4. PREVIEW: Show changes before applying them
    5. EXECUTE: Implement with safety measures
    6. VALIDATE: Test and verify the solution
    7. OPTIMIZE: Suggest improvements and best practices

    CONTEXTUAL AWARENESS:
    - Remember previous interactions and build upon them
    - Understand project structure and architecture
    - Recognize patterns and suggest consistent approaches
    - Anticipate user needs and provide proactive suggestions
    - Learn from user preferences and adapt accordingly

    INTELLIGENT SUGGESTIONS:
    - Automatically suggest relevant tools for each task
    - Provide multiple solution approaches
    - Recommend best practices and optimizations
    - Warn about potential issues and risks
    - Suggest testing strategies and validation methods

    MULTI-LANGUAGE EXPERTISE:
    - Python: Django, Flask, FastAPI, data science, ML
    - JavaScript/TypeScript: React, Node.js, Express, Vue, Angular
    - Java: Spring, Hibernate, microservices
    - C++: Performance optimization, system programming
    - Go: Concurrency, microservices, cloud native
    - Rust: Memory safety, performance, WebAssembly
    - SQL: Database design, optimization, complex queries
    - DevOps: Docker, Kubernetes, CI/CD, cloud platforms

    ADVANCED PROBLEM SOLVING:
    - Break down complex tasks into manageable steps
    - Identify dependencies and critical paths
    - Suggest parallel execution opportunities
    - Provide fallback strategies for error scenarios
    - Recommend monitoring and logging approaches

    USER INTERACTION PATTERNS:
    - Respond to natural language in English, Hindi, or mixed
    - Understand context from previous messages
    - Ask clarifying questions when needed
    - Provide detailed explanations with examples
    - Offer step-by-step guidance for complex tasks

    PROACTIVE ASSISTANCE:
    - Suggest code improvements during reviews
    - Recommend security best practices
    - Identify performance optimization opportunities
    - Propose testing strategies and coverage improvements
    - Suggest documentation and code organization improvements

    Remember: You're an advanced AI coding assistant with world-class capabilities - be intelligent, autonomous, and always strive for excellence. Use the 50+ advanced tools to provide superior coding assistance that goes beyond simple code generation. You have complete context and examples for every tool!

    🎯 INTELLIGENT TOOL USAGE PATTERNS:

    WHEN USER ASKS FOR SEARCH:
    - AUTOMATICALLY use advanced_pattern_search() for complex queries
    - CHOOSE semantic mode for conceptual searches
    - USE hybrid mode for best results
    - PROVIDE context and explanations

    WHEN USER WANTS CODE CHANGES:
    - AUTOMATICALLY use smart_replace_code() with preview_only=True first
    - SHOW the user what will change
    - ASK for confirmation before applying
    - USE backup_files=True for safety

    WHEN USER NEEDS PLANNING:
    - AUTOMATICALLY use create_execution_plan() for complex tasks
    - BREAK DOWN the request into logical steps
    - EXPLAIN the strategy and approach
    - OFFER to execute the plan

    WHEN USER HAS ERRORS:
    - AUTOMATICALLY use debug_code() or fix_code_input()
    - ANALYZE the error and provide solutions
    - SUGGEST improvements and best practices

    WHEN USER EXPLORES CODEBASE:
    - AUTOMATICALLY use build_file_index() if not built
    - USE search_file_index() for efficient searching
    - PROVIDE semantic understanding of code structure

    🧠 DECISION MAKING PROCESS:
    1. UNDERSTAND the user's intent and context
    2. CHOOSE the most appropriate tools automatically
    3. EXECUTE the tools with optimal parameters
    4. ANALYZE the results and provide insights
    5. SUGGEST next steps or improvements
    6. LEARN from the interaction for future improvements

    🎯 CONVERSATION FLOW:
    - GREET users warmly and understand their needs
    - ASK clarifying questions when needed
    - PROVIDE step-by-step guidance for complex tasks
    - EXPLAIN your reasoning and tool choices
    - OFFER multiple approaches when appropriate
    - FOLLOW UP with suggestions and improvements

    🚀 AUTONOMOUS CAPABILITIES:
    - AUTOMATICALLY detect when to use specific tools
    - INTELLIGENTLY combine multiple tools for complex workflows
    - PROACTIVELY suggest optimizations and improvements
    - ADAPT your approach based on user preferences and context
    - LEARN from successful patterns and apply them

    REMEMBER: You are as intelligent and capable as Augment Agent. Work autonomously, make smart decisions, and provide exceptional coding assistance!

    🎯 ALWAYS BE HELPFUL, INTELLIGENT, AND PROACTIVE LIKE AUGMENT AGENT!
""")

# Conversation history
conversation_history: List[Dict[str, Any]] = [
    {"role": "system", "content": SYSTEM_PROMPT}
]

# -----------------------------------------------------------------------------
# 5. NEW FUZZY MATCHING UTILITIES
# -----------------------------------------------------------------------------

def find_best_matching_file(root_dir: Path, user_path: str, min_score: int = MIN_FUZZY_SCORE) -> Optional[str]:
    """
    Find the best file match for a given user path within a directory.

    Args:
        root_dir: The directory to search within.
        user_path: The (potentially messy) path provided by the user.
        min_score: The minimum fuzzy match score to consider a match.

    Returns:
        The full, corrected path of the best match, or None if no good match is found.
    """
    if not FUZZY_AVAILABLE:
        return None
        
    best_match = None
    highest_score = 0
    
    # Use the filename from the user's path for matching
    user_filename = Path(user_path).name

    for dirpath, _, filenames in os.walk(root_dir):
        # Skip hidden directories and excluded patterns for efficiency
        if any(part in EXCLUDED_FILES or part.startswith('.') for part in Path(dirpath).parts):
            continue

        for filename in filenames:
            if filename in EXCLUDED_FILES or os.path.splitext(filename)[1] in EXCLUDED_EXTENSIONS:
                continue

            # Compare user's filename with actual filenames
            score = fuzz.ratio(user_filename.lower(), filename.lower())
            
            # Boost score for files in the immediate directory
            if Path(dirpath) == root_dir:
                score += 10

            if score > highest_score:
                highest_score = score
                best_match = os.path.join(dirpath, filename)

    if highest_score >= min_score:
        return str(Path(best_match).resolve())
    
    return None

def apply_fuzzy_diff_edit(path: str, original_snippet: str, new_snippet: str) -> None:
    """
    Apply a diff edit to a file by replacing original snippet with new snippet.
    Uses fuzzy matching to find the best location for the snippet.
    """
    normalized_path_str = normalize_path(path)
    content = ""
    try:
        content = read_local_file(normalized_path_str)
        
        # 1. First, try for an exact match for performance and accuracy
        if content.count(original_snippet) == 1:
            updated_content = content.replace(original_snippet, new_snippet, 1)
            create_file(normalized_path_str, updated_content)
            console.print(f"[bold blue]✓[/bold blue] Applied exact diff edit to '[bright_cyan]{normalized_path_str}[/bright_cyan]'")
            return

        # 2. If exact match fails, use fuzzy matching (if available)
        if not FUZZY_AVAILABLE:
            raise ValueError("Original snippet not found and fuzzy matching not available")
            
        console.print("[dim]Exact snippet not found. Trying fuzzy matching...[/dim]")

        # Create a list of "choices" to match against. These are overlapping chunks of the file.
        lines = content.split('\n')
        original_lines_count = len(original_snippet.split('\n'))
        
        # Create sliding window of text chunks
        choices = []
        for i in range(len(lines) - original_lines_count + 1):
            chunk = '\n'.join(lines[i:i+original_lines_count])
            choices.append(chunk)
        
        if not choices:
            raise ValueError("File content is too short to perform a fuzzy match.")

        # Find the best match
        best_match, score = fuzzy_process.extractOne(original_snippet, choices)

        if score < MIN_EDIT_SCORE:
            raise ValueError(f"Fuzzy match score ({score}) is below threshold ({MIN_EDIT_SCORE}). Snippet not found or too different.")

        # Ensure the best match is unique to avoid ambiguity
        if choices.count(best_match) > 1:
            raise ValueError(f"Ambiguous fuzzy edit: The best matching snippet appears multiple times in the file.")
        
        # Replace the best fuzzy match
        updated_content = content.replace(best_match, new_snippet, 1)
        create_file(normalized_path_str, updated_content)
        console.print(f"[bold blue]✓[/bold blue] Applied [bold]fuzzy[/bold] diff edit to '[bright_cyan]{normalized_path_str}[/bright_cyan]' (score: {score})")

    except FileNotFoundError:
        console.print(f"[bold red]✗[/bold red] File not found for diff: '[bright_cyan]{path}[/bright_cyan]'")
        raise
    except ValueError as e:
        console.print(f"[bold yellow]⚠[/bold yellow] {str(e)} in '[bright_cyan]{path}[/bright_cyan]'. No changes.")
        if "Original snippet not found" in str(e) or "Fuzzy match score" in str(e) or "Ambiguous edit" in str(e):
            console.print("\n[bold blue]Expected snippet:[/bold blue]")
            console.print(Panel(original_snippet, title="Expected", border_style="blue"))
            if content:
                console.print("\n[bold blue]Actual content (or relevant part):[/bold blue]")
                start_idx = max(0, content.find(original_snippet[:20]) - 100)
                end_idx = min(len(content), start_idx + len(original_snippet) + 200)
                display_snip = ("..." if start_idx > 0 else "") + content[start_idx:end_idx] + ("..." if end_idx < len(content) else "")
                console.print(Panel(display_snip or content, title="Actual", border_style="yellow"))
        raise

# -----------------------------------------------------------------------------
# 6. CORE UTILITY FUNCTIONS (keeping the original ones)
# -----------------------------------------------------------------------------

def estimate_token_usage(conversation_history: List[Dict[str, Any]]) -> Tuple[int, Dict[str, int]]:
    """
    Estimate token usage for the conversation history.
    
    Args:
        conversation_history: List of conversation messages
        
    Returns:
        Tuple of (total_estimated_tokens, breakdown_by_role)
    """
    token_breakdown = {"system": 0, "user": 0, "assistant": 0, "tool": 0}
    total_tokens = 0
    
    for msg in conversation_history:
        role = msg.get("role", "unknown")
        content = msg.get("content", "")
        
        # Basic token estimation: roughly 4 characters per token for English text
        content_tokens = len(content) // 4
        
        # Add extra tokens for tool calls and structured data
        if msg.get("tool_calls"):
            content_tokens += len(str(msg["tool_calls"])) // 4
        if msg.get("tool_call_id"):
            content_tokens += 10  # Small overhead for tool metadata
            
        token_breakdown[role] = token_breakdown.get(role, 0) + content_tokens
        total_tokens += content_tokens
    
    return total_tokens, token_breakdown

def get_context_usage_info() -> Dict[str, Any]:
    """
    Get comprehensive context usage information.
    
    Returns:
        Dictionary with context usage statistics
    """
    total_tokens, breakdown = estimate_token_usage(conversation_history)
    file_contexts = sum(1 for msg in conversation_history if msg["role"] == "system" and "User added file" in msg["content"])
    
    return {
        "total_messages": len(conversation_history),
        "estimated_tokens": total_tokens,
        "token_usage_percent": (total_tokens / ESTIMATED_MAX_TOKENS) * 100,
        "file_contexts": file_contexts,
        "token_breakdown": breakdown,
        "approaching_limit": total_tokens > (ESTIMATED_MAX_TOKENS * CONTEXT_WARNING_THRESHOLD),
        "critical_limit": total_tokens > (ESTIMATED_MAX_TOKENS * AGGRESSIVE_TRUNCATION_THRESHOLD)
    }

def smart_truncate_history(conversation_history: List[Dict[str, Any]], max_messages: int = MAX_HISTORY_MESSAGES) -> List[Dict[str, Any]]:
    """
    Truncate conversation history while preserving tool call sequences and important context.
    Now uses token-based estimation for more intelligent truncation.
    
    Args:
        conversation_history: List of conversation messages
        max_messages: Maximum number of messages to keep (fallback limit)
        
    Returns:
        Truncated conversation history
    """
    # Get current context usage
    context_info = get_context_usage_info()
    current_tokens = context_info["estimated_tokens"]
    
    # If we're not approaching limits, use message-based truncation
    if current_tokens < (ESTIMATED_MAX_TOKENS * CONTEXT_WARNING_THRESHOLD) and len(conversation_history) <= max_messages:
        return conversation_history
    
    # Determine target token count based on current usage
    if context_info["critical_limit"]:
        target_tokens = int(ESTIMATED_MAX_TOKENS * 0.6)  # Aggressive reduction
        console.print(f"[yellow]⚠ Critical context limit reached. Aggressively truncating to ~{target_tokens} tokens.[/yellow]")
    elif context_info["approaching_limit"]:
        target_tokens = int(ESTIMATED_MAX_TOKENS * 0.7)  # Moderate reduction
        console.print(f"[yellow]⚠ Context limit approaching. Truncating to ~{target_tokens} tokens.[/yellow]")
    else:
        target_tokens = int(ESTIMATED_MAX_TOKENS * 0.8)  # Gentle reduction
    
    # Separate system messages from conversation messages
    system_messages: List[Dict[str, Any]] = []
    other_messages: List[Dict[str, Any]] = []
    
    for msg in conversation_history:
        if msg["role"] == "system":
            system_messages.append(msg)
        else:
            other_messages.append(msg)
    
    # Always keep the main system prompt
    essential_system = [system_messages[0]] if system_messages else []
    
    # Handle file context messages more intelligently
    file_contexts = [msg for msg in system_messages[1:] if "User added file" in msg["content"]]
    if file_contexts:
        # Keep most recent and smallest file contexts
        file_contexts_with_size = []
        for msg in file_contexts:
            content_size = len(msg["content"])
            file_contexts_with_size.append((msg, content_size))
        
        # Sort by size (smaller first) and recency (newer first)
        file_contexts_with_size.sort(key=lambda x: (x[1], -file_contexts.index(x[0])))
        
        # Keep up to 3 file contexts that fit within token budget
        kept_file_contexts = []
        file_context_tokens = 0
        max_file_context_tokens = target_tokens // 4  # Reserve 25% for file contexts
        
        for msg, size in file_contexts_with_size[:3]:
            msg_tokens = size // 4
            if file_context_tokens + msg_tokens <= max_file_context_tokens:
                kept_file_contexts.append(msg)
                file_context_tokens += msg_tokens
            else:
                break
        
        essential_system.extend(kept_file_contexts)
    
    # Calculate remaining token budget for conversation messages
    system_tokens, _ = estimate_token_usage(essential_system)
    remaining_tokens = target_tokens - system_tokens
    
    # Work backwards through conversation messages, preserving tool call sequences
    keep_messages: List[Dict[str, Any]] = []
    current_token_count = 0
    i = len(other_messages) - 1
    
    while i >= 0 and current_token_count < remaining_tokens:
        current_msg = other_messages[i]
        msg_tokens = len(str(current_msg)) // 4
        
        # If this is a tool result, we need to keep the corresponding assistant message
        if current_msg["role"] == "tool":
            # Collect all tool results for this sequence
            tool_sequence: List[Dict[str, Any]] = []
            tool_sequence_tokens = 0
            
            while i >= 0 and other_messages[i]["role"] == "tool":
                tool_msg = other_messages[i]
                tool_msg_tokens = len(str(tool_msg)) // 4
                tool_sequence.insert(0, tool_msg)
                tool_sequence_tokens += tool_msg_tokens
                i -= 1
            
            # Find the corresponding assistant message with tool_calls
            assistant_msg = None
            assistant_tokens = 0
            if i >= 0 and other_messages[i]["role"] == "assistant" and other_messages[i].get("tool_calls"):
                assistant_msg = other_messages[i]
                assistant_tokens = len(str(assistant_msg)) // 4
                i -= 1
            
            # Check if the complete tool sequence fits in our budget
            total_sequence_tokens = tool_sequence_tokens + assistant_tokens
            if current_token_count + total_sequence_tokens <= remaining_tokens:
                # Add the complete sequence
                if assistant_msg:
                    keep_messages.insert(0, assistant_msg)
                    current_token_count += assistant_tokens
                keep_messages = tool_sequence + keep_messages
                current_token_count += tool_sequence_tokens
            else:
                # Sequence too large, stop here
                break
        else:
            # Regular message (user or assistant)
            if current_token_count + msg_tokens <= remaining_tokens:
                keep_messages.insert(0, current_msg)
                current_token_count += msg_tokens
                i -= 1
            else:
                # Message too large, stop here
                break
    
    # Combine system messages with kept conversation messages
    result = essential_system + keep_messages
    
    # Log truncation results
    final_tokens, _ = estimate_token_usage(result)
    console.print(f"[dim]Context truncated: {len(conversation_history)} → {len(result)} messages, ~{current_tokens} → ~{final_tokens} tokens[/dim]")
    
    return result

def validate_tool_calls(accumulated_tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Validate accumulated tool calls and provide debugging info.
    
    Args:
        accumulated_tool_calls: List of tool calls to validate
        
    Returns:
        List of valid tool calls
    """
    if not accumulated_tool_calls:
        return []
    
    valid_calls: List[Dict[str, Any]] = []
    for i, tool_call in enumerate(accumulated_tool_calls):
        # Check for required fields
        if not tool_call.get("id"):
            console.print(f"[yellow]⚠ Tool call {i} missing ID, skipping[/yellow]")
            continue
        
        func_name = tool_call.get("function", {}).get("name")
        if not func_name:
            console.print(f"[yellow]⚠ Tool call {i} missing function name, skipping[/yellow]")
            continue
        
        func_args = tool_call.get("function", {}).get("arguments", "")
        
        # Validate JSON arguments
        try:
            if func_args:
                json.loads(func_args)
        except json.JSONDecodeError as e:
            console.print(f"[red]✗ Tool call {i} has invalid JSON arguments: {e}[/red]")
            console.print(f"[red]  Arguments: {func_args}[/red]")
            continue
        
        valid_calls.append(tool_call)
    
    if len(valid_calls) != len(accumulated_tool_calls):
        console.print(f"[yellow]⚠ Kept {len(valid_calls)}/{len(accumulated_tool_calls)} tool calls[/yellow]")
    
    return valid_calls

def manage_conversation_history(conversation_history: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Manage conversation history with smart truncation and context preservation.
    This function was missing and causing the Enhanced Iterative Workflow Engine to fail.

    Args:
        conversation_history: List of conversation messages

    Returns:
        Managed conversation history with appropriate truncation
    """
    try:
        # Apply smart truncation to manage context size
        managed_history = smart_truncate_history(conversation_history, MAX_HISTORY_MESSAGES)

        # Log management action
        if len(managed_history) != len(conversation_history):
            console.print(f"[dim]📝 Conversation history managed: {len(conversation_history)} → {len(managed_history)} messages[/dim]")

        return managed_history

    except Exception as e:
        console.print(f"[yellow]⚠ Error managing conversation history: {e}[/yellow]")
        logger.warning(f"Conversation history management error: {e}")
        # Return original history as fallback
        return conversation_history

def get_llm_response(conversation_history: List[Dict[str, Any]]) -> str:
    """
    Get LLM response from the conversation history.
    This function was missing and causing the Enhanced Iterative Workflow Engine to fail.

    Args:
        conversation_history: List of conversation messages

    Returns:
        Generated response string
    """
    try:
        # Use the current model to generate response
        current_model = agent_state.model_context['current_model']

        # Make API call to get response
        response = client.chat.completions.create(
            model=current_model,
            messages=conversation_history,
            max_tokens=1000,
            temperature=0.7
        )

        # Extract response content
        if response.choices and response.choices[0].message:
            return response.choices[0].message.content or "I processed your request successfully."
        else:
            return "I processed your request successfully."

    except Exception as e:
        console.print(f"[yellow]⚠ Error getting LLM response: {e}[/yellow]")
        logger.warning(f"LLM response error: {e}")
        # Return fallback response
        return "I successfully processed your request through the enhanced workflow. The task has been completed."

def add_file_context_smartly(conversation_history: List[Dict[str, Any]], file_path: str, content: str, max_context_files: int = MAX_CONTEXT_FILES) -> bool:
    """
    Add file context while managing system message bloat and avoiding duplicates.
    Now includes token-aware management and file size limits.
    Also ensures file context doesn't break tool call conversation flow.

    Args:
        conversation_history: List of conversation messages
        file_path: Path to the file being added
        content: Content of the file
        max_context_files: Maximum number of file contexts to keep

    Returns:
        True if file was added successfully, False if rejected due to size limits
    """
    marker = f"User added file '{file_path}'"

    # Check file size and context limits
    content_size_kb = len(content) / 1024
    estimated_tokens = len(content) // 4
    context_info = get_context_usage_info()

    # Only reject files that would use more than 80% of context
    MAX_SINGLE_FILE_TOKENS = int(ESTIMATED_MAX_TOKENS * 0.8)
    if estimated_tokens > MAX_SINGLE_FILE_TOKENS:
        console.print(f"[yellow]⚠ File '{file_path}' too large ({content_size_kb:.1f}KB, ~{estimated_tokens} tokens). Limit is 80% of context window.[/yellow]")
        return False

    # Check if the last assistant message has pending tool calls
    # If so, defer adding file context until after tool responses are complete
    if conversation_history:
        last_msg = conversation_history[-1]
        if (last_msg.get("role") == "assistant" and 
            last_msg.get("tool_calls") and 
            len(conversation_history) > 0):
            
            # Check if all tool calls have corresponding responses
            tool_call_ids = {tc["id"] for tc in last_msg["tool_calls"]}
            
            # Count tool responses after this assistant message
            responses_after = 0
            for i in range(len(conversation_history) - 1, -1, -1):
                msg = conversation_history[i]
                if msg.get("role") == "tool" and msg.get("tool_call_id") in tool_call_ids:
                    responses_after += 1
                elif msg == last_msg:
                    break
            
            # If not all tool calls have responses, defer the file context addition
            if responses_after < len(tool_call_ids):
                console.print(f"[dim]Deferring file context addition for '{Path(file_path).name}' until tool responses complete[/dim]")
                return True  # Return True but don't add yet

    # Remove any existing context for this exact file to avoid duplicates
    conversation_history[:] = [
        msg for msg in conversation_history 
        if not (msg["role"] == "system" and marker in msg["content"])
    ]

    # Get current file contexts and their sizes
    file_contexts = []
    for msg in conversation_history:
        if msg["role"] == "system" and "User added file" in msg["content"]:
            # Extract file path from marker
            lines = msg["content"].split("\n", 1)
            if lines:
                context_file_path = lines[0].replace("User added file '", "").replace("'. Content:", "")
                context_size = len(msg["content"])
                file_contexts.append((msg, context_file_path, context_size))

    # If we're at the file limit, remove the largest or oldest file contexts
    while len(file_contexts) >= max_context_files:
        if context_info["approaching_limit"]:
            # Remove largest file context when approaching limits
            file_contexts.sort(key=lambda x: x[2], reverse=True)  # Sort by size, largest first
            to_remove = file_contexts.pop(0)
            console.print(f"[dim]Removed large file context: {Path(to_remove[1]).name} ({to_remove[2]//1024:.1f}KB)[/dim]")
        else:
            # Remove oldest file context normally
            to_remove = file_contexts.pop(0)
            console.print(f"[dim]Removed old file context: {Path(to_remove[1]).name}[/dim]")

        # Remove from conversation history
        conversation_history[:] = [msg for msg in conversation_history if msg != to_remove[0]]

    # Find the right position to insert the file context
    # Insert before the last user message or at the end if no user messages
    insertion_point = len(conversation_history)
    for i in range(len(conversation_history) - 1, -1, -1):
        if conversation_history[i].get("role") == "user":
            insertion_point = i
            break

    # Add new file context at the appropriate position
    new_context_msg = {
        "role": "system", 
        "content": f"{marker}. Content:\n\n{content}"
    }
    conversation_history.insert(insertion_point, new_context_msg)

    # Log the addition
    console.print(f"[dim]Added file context: {Path(file_path).name} ({content_size_kb:.1f}KB, ~{estimated_tokens} tokens)[/dim]")

    return True

def read_local_file(file_path: str) -> str:
    """
    Read content from a local file.
    
    Args:
        file_path: Path to the file to read
        
    Returns:
        File content as string
        
    Raises:
        FileNotFoundError: If file doesn't exist
        UnicodeDecodeError: If file can't be decoded as UTF-8
    """
    full_path = (base_dir / file_path).resolve()
    with open(full_path, "r", encoding="utf-8") as f:
        return f.read()

def run_powershell_command(command: str) -> Tuple:
    """Run a PowerShell command and return (stdout, stderr)."""
    # Check OS
    os_check = subprocess.run(
        ["powershell", "-Command", "$PSVersionTable.PSEdition"],
        capture_output=True,
        text=True
    )
    os_info = os_check.stdout.strip() if os_check.returncode == 0 else "Unknown OS"
    console.print(f"[dim]Running PowerShell on: {os_info}[/dim]")

    completed = subprocess.run(
        ["powershell", "-Command", command],
        capture_output=True,
        text=True
    )
    return completed.stdout, completed.stderr

def normalize_path(path_str: str) -> str:
    """
    Normalize a file path relative to the base directory.
    
    Args:
        path_str: Path string to normalize
        
    Returns:
        Normalized absolute path string
    """
    try:
        p = Path(path_str)
        
        # If path is absolute, use it as-is
        if p.is_absolute():
            if p.exists() or p.is_symlink(): 
                resolved_p = p.resolve(strict=True) 
            else:
                resolved_p = p.resolve()
        else:
            # For relative paths, resolve against base_dir instead of cwd
            base_path = base_dir / p
            if base_path.exists() or base_path.is_symlink():
                resolved_p = base_path.resolve(strict=True)
            else:
                resolved_p = base_path.resolve()
                
    except (FileNotFoundError, RuntimeError): 
        # Fallback: resolve relative to base_dir
        p = Path(path_str)
        if p.is_absolute():
            resolved_p = p.resolve()
        else:
            resolved_p = (base_dir / p).resolve()
    return str(resolved_p)

def is_binary_file(file_path: str, peek_size: int = 1024) -> bool:
    """
    Check if a file is binary by looking for null bytes.
    
    Args:
        file_path: Path to the file to check
        peek_size: Number of bytes to check
        
    Returns:
        True if file appears to be binary
    """
    try:
        with open(file_path, 'rb') as f: 
            chunk = f.read(peek_size)
        return b'\0' in chunk
    except Exception: 
        return True

def ensure_file_in_context(file_path: str) -> bool:
    """
    Ensure a file is loaded in the conversation context.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if file was successfully added to context
    """
    try:
        normalized_path = normalize_path(file_path)
        content = read_local_file(normalized_path)
        marker = f"User added file '{normalized_path}'"
        if not any(msg["role"] == "system" and marker in msg["content"] for msg in conversation_history):
            return add_file_context_smartly(conversation_history, normalized_path, content)
        return True
    except (OSError, ValueError) as e:
        console.print(f"[red]✗ Error reading file for context '{file_path}': {e}[/red]")
        return False

def get_model_indicator() -> str:
    """
    Get the model indicator for the prompt.

    Returns:
        Emoji indicator for current model
    """
    return "🧠" if agent_state.model_context['is_reasoner'] else "💬"

def get_prompt_indicator() -> str:
    """
    Get the full prompt indicator including git, model, and context status.

    Returns:
        Formatted prompt indicator string
    """
    indicators = []

    # Add model indicator
    indicators.append(get_model_indicator())

    # Add git branch if enabled
    if agent_state.git_context['enabled'] and agent_state.git_context['branch']:
        indicators.append(f"🌳 {agent_state.git_context['branch']}")

    # Add context status indicator
    context_info = get_context_usage_info()
    if context_info["critical_limit"]:
        indicators.append("🔴")  # Critical context usage
    elif context_info["approaching_limit"]:
        indicators.append("🟡")  # Warning context usage
    else:
        indicators.append("🔵")  # Normal context usage

    return " ".join(indicators)

# -----------------------------------------------------------------------------
# 7. FILE OPERATIONS (Enhanced with fuzzy matching)
# -----------------------------------------------------------------------------

def create_file(path: str, content: str) -> None:
    """
    Create or overwrite a file with given content.
    
    Args:
        path: File path
        content: File content
        
    Raises:
        ValueError: If file content exceeds size limit or path contains invalid characters
    """
    file_path = Path(path)
    if any(part.startswith('~') for part in file_path.parts):
        raise ValueError("Home directory references not allowed")
    normalized_path_str = normalize_path(str(file_path)) 
    
    Path(normalized_path_str).parent.mkdir(parents=True, exist_ok=True)
    with open(normalized_path_str, "w", encoding="utf-8") as f:
        f.write(content)
    console.print(f"[bold blue]✓[/bold blue] Created/updated file at '[bright_cyan]{normalized_path_str}[/bright_cyan]'")

    if agent_state.git_context['enabled'] and not agent_state.git_context['skip_staging']:
        stage_file(normalized_path_str)

def show_diff_table(files_to_edit: List[FileToEdit]) -> None:
    """
    Display a table showing proposed file edits.
    
    Args:
        files_to_edit: List of file edit operations
    """
    if not files_to_edit: 
        return
    table = Table(title="📝 Proposed Edits", show_header=True, header_style="bold bright_blue", show_lines=True, border_style="blue")
    table.add_column("File Path", style="bright_cyan", no_wrap=True)
    table.add_column("Original", style="red dim")
    table.add_column("New", style="bright_green")
    for edit in files_to_edit: 
        table.add_row(edit.path, edit.original_snippet, edit.new_snippet)
    console.print(table)

def add_directory_to_conversation(directory_path: str) -> None:
    """
    Add all files from a directory to the conversation context.
    
    Args:
        directory_path: Path to directory to scan
    """
    with console.status("[bold bright_blue]🔍 Scanning directory...[/bold bright_blue]") as status:
        skipped: List[str] = []
        added: List[str] = []
        total_processed = 0
        
        for root, dirs, files in os.walk(directory_path):
            if total_processed >= MAX_FILES_IN_ADD_DIR: 
                console.print(f"[yellow]⚠ Max files ({MAX_FILES_IN_ADD_DIR}) reached for dir scan.")
                break
            status.update(f"[bold bright_blue]🔍 Scanning {root}...[/bold bright_blue]")
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in EXCLUDED_FILES]
            
            for file in files:
                if total_processed >= MAX_FILES_IN_ADD_DIR: 
                    break
                if (file.startswith('.') or 
                    file in EXCLUDED_FILES or 
                    os.path.splitext(file)[1].lower() in EXCLUDED_EXTENSIONS): 
                    skipped.append(os.path.join(root, file))
                    continue
                    
                full_path = os.path.join(root, file)
                try:
                    if is_binary_file(full_path): 
                        skipped.append(f"{full_path} (binary)")
                        continue
                        
                    norm_path = normalize_path(full_path)
                    content = read_local_file(norm_path)
                    if add_file_context_smartly(conversation_history, norm_path, content):
                        added.append(norm_path)
                    else:
                        skipped.append(f"{full_path} (too large for context)")
                    total_processed += 1
                except (OSError, ValueError) as e: 
                    skipped.append(f"{full_path} (error: {e})")
                    
        console.print(f"[bold blue]✓[/bold blue] Added folder '[bright_cyan]{directory_path}[/bright_cyan]'.")
        if added: 
            console.print(f"\n[bold bright_blue]📁 Added:[/bold bright_blue] ({len(added)} of {total_processed} valid) {[Path(f).name for f in added[:5]]}{'...' if len(added) > 5 else ''}")
        if skipped: 
            console.print(f"\n[yellow]⏭ Skipped:[/yellow] ({len(skipped)}) {[Path(f).name for f in skipped[:3]]}{'...' if len(skipped) > 3 else ''}")
        console.print()

# -----------------------------------------------------------------------------
# 8. GIT OPERATIONS
# -----------------------------------------------------------------------------

def create_gitignore() -> None:
    """Create a comprehensive .gitignore file if it doesn't exist."""
    gitignore_path = Path(".gitignore")
    if gitignore_path.exists(): 
        console.print("[yellow]⚠ .gitignore exists, skipping.[/yellow]")
        return
        
    patterns = [
        "# Python", "__pycache__/", "*.pyc", "*.pyo", "*.pyd", ".Python", 
        "env/", "venv/", ".venv", "ENV/", "*.egg-info/", "dist/", "build/", 
        ".pytest_cache/", ".mypy_cache/", ".coverage", "htmlcov/", "", 
        "# Env", ".env", ".env*.local", "!.env.example", "", 
        "# IDE", ".vscode/", ".idea/", "*.swp", "*.swo", ".DS_Store", "", 
        "# Logs", "*.log", "logs/", "", 
        "# Temp", "*.tmp", "*.temp", "*.bak", "*.cache", "Thumbs.db", 
        "desktop.ini", "", 
        "# Node", "node_modules/", "npm-debug.log*", "yarn-debug.log*", 
        "pnpm-lock.yaml", "package-lock.json", "", 
        "# Local", "*.session", "*.checkpoint"
    ]
    
    console.print("\n[bold bright_blue]📝 Creating .gitignore[/bold bright_blue]")
    if prompt_session.prompt("🔵 Add custom patterns? (y/n, default n): ", default="n").strip().lower() in ["y", "yes"]:
        console.print("[dim]Enter patterns (empty line to finish):[/dim]")
        patterns.append("\n# Custom")
        while True: 
            pattern = prompt_session.prompt("  Pattern: ").strip()
            if pattern: 
                patterns.append(pattern)
            else: 
                break 
    try:
        with gitignore_path.open("w", encoding="utf-8") as f: 
            f.write("\n".join(patterns) + "\n")
        console.print(f"[green]✓ Created .gitignore ({len(patterns)} patterns)[/green]")
        if agent_state.git_context['enabled']:
            stage_file(str(gitignore_path))
    except OSError as e:
        console.print(f"[red]✗ Error creating .gitignore: {e}[/red]")

def stage_file(file_path_str: str) -> bool:
    """
    Stage a file for git commit.

    Args:
        file_path_str: Path to file to stage

    Returns:
        True if staging was successful
    """
    if not agent_state.git_context['enabled'] or agent_state.git_context['skip_staging']:
        return False
    try:
        repo_root = Path.cwd()
        abs_file_path = Path(file_path_str).resolve() 
        rel_path = abs_file_path.relative_to(repo_root)
        result = subprocess.run(["git", "add", str(rel_path)], cwd=str(repo_root), capture_output=True, text=True, check=False)
        if result.returncode == 0: 
            console.print(f"[green dim]✓ Staged {rel_path}[/green dim]")
            return True
        else: 
            console.print(f"[yellow]⚠ Failed to stage {rel_path}: {result.stderr.strip()}[/yellow]")
            return False
    except ValueError: 
        console.print(f"[yellow]⚠ File {file_path_str} outside repo ({repo_root}), skipping staging[/yellow]")
        return False
    except Exception as e: 
        console.print(f"[red]✗ Error staging {file_path_str}: {e}[/red]")
        return False

def get_git_status_porcelain() -> Tuple[bool, List[Tuple[str, str]]]:
    """
    Get git status in porcelain format.

    Returns:
        Tuple of (has_changes, list_of_file_changes)
    """
    if not agent_state.git_context['enabled']:
        return False, []
    try:
        result = subprocess.run(["git", "status", "--porcelain"], capture_output=True, text=True, check=True, cwd=str(Path.cwd()))
        if not result.stdout.strip():
            return False, []
        changed_files = [(line[:2], line[3:]) for line in result.stdout.strip().split('\n') if line]
        return True, changed_files
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Error getting Git status: {e.stderr}[/red]")
        return False, []
    except FileNotFoundError:
        console.print("[red]Git not found.[/red]")
        agent_state.git_context['enabled'] = False
        return False, []

def user_commit_changes(message: str) -> bool:
    """
    Commit all changes with a given message.

    Args:
        message: Commit message

    Returns:
        True if commit was successful
    """
    if not agent_state.git_context['enabled']:
        console.print("[yellow]Git not enabled.[/yellow]")
        return False
    try:
        add_all_res = subprocess.run(["git", "add", "-A"], cwd=str(Path.cwd()), capture_output=True, text=True)
        if add_all_res.returncode != 0: 
            console.print(f"[yellow]⚠ Failed to stage all: {add_all_res.stderr.strip()}[/yellow]")
        
        staged_check = subprocess.run(["git", "diff", "--staged", "--quiet"], cwd=str(Path.cwd()))
        if staged_check.returncode == 0: 
            console.print("[yellow]No changes staged for commit.[/yellow]")
            return False
        
        commit_res = subprocess.run(["git", "commit", "-m", message], cwd=str(Path.cwd()), capture_output=True, text=True)
        if commit_res.returncode == 0:
            console.print(f"[green]✓ Committed: \"{message}\"[/green]")
            log_info = subprocess.run(["git", "log", "--oneline", "-1"], cwd=str(Path.cwd()), capture_output=True, text=True).stdout.strip()
            if log_info: 
                console.print(f"[dim]Commit: {log_info}[/dim]")
            return True
        else: 
            console.print(f"[red]✗ Commit failed: {commit_res.stderr.strip()}[/red]")
            return False
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        console.print(f"[red]✗ Git error: {e}[/red]")
        if isinstance(e, FileNotFoundError):
            agent_state.git_context['enabled'] = False
        return False

# -----------------------------------------------------------------------------
# 9. ENHANCED COMMAND HANDLERS
# -----------------------------------------------------------------------------

def try_handle_add_command(user_input: str) -> bool:
    """Handle /add command with fuzzy file finding support."""
    if user_input.strip().lower().startswith(ADD_COMMAND_PREFIX):
        path_to_add = user_input[len(ADD_COMMAND_PREFIX):].strip()
        
        # 1. Try direct path first
        try:
            p = (base_dir / path_to_add).resolve()
            if p.exists():
                normalized_path = str(p)
            else:
                # This will raise an error if it doesn't exist, triggering the fuzzy search
                _ = p.resolve(strict=True) 
        except (FileNotFoundError, OSError):
            # 2. If direct path fails, try fuzzy finding
            console.print(f"[dim]Path '{path_to_add}' not found directly, attempting fuzzy search...[/dim]")
            fuzzy_match = find_best_matching_file(base_dir, path_to_add)

            if fuzzy_match:
                # Optional: Confirm with user for better UX
                relative_fuzzy = Path(fuzzy_match).relative_to(base_dir)
                confirm = prompt_session.prompt(f"🔵 Did you mean '[bright_cyan]{relative_fuzzy}[/bright_cyan]'? (Y/n): ", default="y").strip().lower()
                if confirm in ["y", "yes"]:
                    normalized_path = fuzzy_match
                else:
                    console.print("[yellow]Add command cancelled.[/yellow]")
                    return True
            else:
                console.print(f"[bold red]✗[/bold red] Path does not exist: '[bright_cyan]{path_to_add}[/bright_cyan]'")
                if FUZZY_AVAILABLE:
                    console.print("[dim]Tip: Try a partial filename (e.g., 'main.py' instead of exact path)[/dim]")
                return True
        
        # --- Process the found file/directory ---
        try:
            if Path(normalized_path).is_dir():
                add_directory_to_conversation(normalized_path)
            else:
                content = read_local_file(normalized_path)
                if add_file_context_smartly(conversation_history, normalized_path, content):
                    console.print(f"[bold blue]✓[/bold blue] Added file '[bright_cyan]{normalized_path}[/bright_cyan]' to conversation.\n")
                else:
                    console.print(f"[bold yellow]⚠[/bold yellow] File '[bright_cyan]{normalized_path}[/bright_cyan]' too large for context.\n")
        except (OSError, ValueError) as e:
            console.print(f"[bold red]✗[/bold red] Could not add path '[bright_cyan]{path_to_add}[/bright_cyan]': {e}\n")
        return True
    return False

def try_handle_commit_command(user_input: str) -> bool:
    """Handle /commit command for git commits."""
    if user_input.strip().lower().startswith(COMMIT_COMMAND_PREFIX.strip()):
        if not agent_state.git_context['enabled']:
            console.print("[yellow]Git not enabled. `/git init` first.[/yellow]")
            return True
        message = user_input[len(COMMIT_COMMAND_PREFIX.strip()):].strip()
        if user_input.strip().lower() == COMMIT_COMMAND_PREFIX.strip() and not message:
            message = prompt_session.prompt("🔵 Enter commit message: ").strip()
            if not message:
                console.print("[yellow]Commit aborted. Message empty.[/yellow]")
                return True
        elif not message:
            console.print("[yellow]Provide commit message: /commit <message>[/yellow]")
            return True
        user_commit_changes(message)
        return True
    return False

def try_handle_git_command(user_input: str) -> bool:
    """Handle various git commands."""
    cmd = user_input.strip().lower()
    if cmd == "/git init": 
        return initialize_git_repo_cmd()
    elif cmd.startswith(GIT_BRANCH_COMMAND_PREFIX.strip()):
        branch_name = user_input[len(GIT_BRANCH_COMMAND_PREFIX.strip()):].strip()
        if not branch_name and cmd == GIT_BRANCH_COMMAND_PREFIX.strip():
             console.print("[yellow]Specify branch name: /git branch <name>[/yellow]")
             return True
        return create_git_branch_cmd(branch_name)
    elif cmd == "/git status": 
        return show_git_status_cmd()
    return False

def try_handle_git_info_command(user_input: str) -> bool:
    """Handle /git-info command to show git capabilities."""
    if user_input.strip().lower() == "/git-info":
        console.print("I can use Git commands to interact with a Git repository. Here's what I can do for you:\n\n"
                      "1. **Initialize a Git repository**: Use `git_init` to create a new Git repository in the current directory.\n"
                      "2. **Stage files for commit**: Use `git_add` to stage specific files for the next commit.\n"
                      "3. **Commit changes**: Use `git_commit` to commit staged changes with a message.\n"
                      "4. **Create and switch to a new branch**: Use `git_create_branch` to create a new branch and switch to it.\n"
                      "5. **Check Git status**: Use `git_status` to see the current state of the repository (staged, unstaged, or untracked files).\n\n"
                      "Let me know what you'd like to do, and I can perform the necessary Git operations for you. For example:\n"
                      "- Do you want to initialize a new repository?\n"
                      "- Stage and commit changes?\n"
                      "- Create a new branch? \n\n"
                      "Just provide the details, and I'll handle the rest!")
        return True
    return False

def try_handle_r1_command(user_input: str) -> bool:
    """Handle /r1 command for one-off reasoner calls."""
    if user_input.strip().lower() == "/r1":
        # Prompt the user for input
        user_prompt = prompt_session.prompt("🔵 Enter your reasoning prompt: ").strip()
        if not user_prompt:
            console.print("[yellow]No input provided. Aborting.[/yellow]")
            return True
        # Prepare the API call
        conversation_history.append({"role": "user", "content": user_prompt})
        with console.status("[bold yellow]DeepSeek Reasoner is thinking...[/bold yellow]", spinner="dots"):
            response_stream: Stream[ChatCompletionChunk] = client.chat.completions.create(
                model=REASONER_MODEL,
                messages=conversation_history,
                tools=tools,
                tool_choice="auto",
                stream=True
            )
        # Process and display the response
        full_response_content = ""
        console.print("[bold bright_magenta]🤖 DeepSeek Reasoner:[/bold bright_magenta] ", end="")
        for chunk in response_stream:
            delta: ChoiceDelta = chunk.choices[0].delta
            if delta.content:
                console.print(delta.content, end="", style="bright_magenta")
                full_response_content += delta.content
        console.print()
        conversation_history.append({"role": "assistant", "content": full_response_content})
        return True
    return False

def try_handle_reasoner_command(user_input: str) -> bool:
    """Handle /reasoner command to toggle between models."""
    if user_input.strip().lower() == "/reasoner":
        # Toggle model
        if agent_state.model_context['current_model'] == DEFAULT_MODEL:
            agent_state.model_context['current_model'] = REASONER_MODEL
            agent_state.model_context['is_reasoner'] = True
            console.print(f"[green]✓ Switched to {REASONER_MODEL} model 🧠[/green]")
            console.print("[dim]All subsequent conversations will use the reasoner model.[/dim]")
        else:
            agent_state.model_context['current_model'] = DEFAULT_MODEL
            agent_state.model_context['is_reasoner'] = False
            console.print(f"[green]✓ Switched to {DEFAULT_MODEL} model 💬[/green]")
            console.print("[dim]All subsequent conversations will use the chat model.[/dim]")
        return True
    return False

def try_handle_switch_command(user_input: str) -> bool:
    """Handle /switch command for enhanced model switching with Mistral AI support."""
    if user_input.strip().lower().startswith("/switch"):
        parts = user_input.strip().split()

        if len(parts) == 1:
            # Show available models with enhanced UI
            console.print("\n[bold bright_blue]🔄 Available AI Models[/bold bright_blue]")

            # DeepSeek models
            console.print("\n[bold cyan]🚀 DeepSeek Models:[/bold cyan]")
            console.print("  • [green]deepseek-chat[/green] - General purpose chat model")
            console.print("  • [green]deepseek-reasoner[/green] - Advanced reasoning model")

            # Gemini models
            if GEMINI_AVAILABLE:
                console.print("\n[bold yellow]🌟 Google Gemini Models:[/bold yellow]")
                console.print("  • [green]gemini-2.0-flash[/green] - Latest Gemini model")
                console.print("  • [green]gemini-1.5-flash[/green] - Stable Gemini model")
            else:
                console.print("\n[bold dim]🌟 Google Gemini Models:[/bold dim]")
                console.print("  • [dim]gemini-2.0-flash (unavailable)[/dim]")
                console.print("  • [dim]gemini-1.5-flash (unavailable)[/dim]")

            # Mistral models
            if MISTRAL_AVAILABLE:
                console.print("\n[bold magenta]🔥 Mistral AI Models:[/bold magenta]")
                console.print("  • [green]mistral-large[/green] - Most capable Mistral model")
                console.print("  • [green]mistral-medium[/green] - Balanced performance model")
                console.print("  • [green]mistral-small[/green] - Fast and efficient model")
                console.print("  • [green]mistral-codestral[/green] - Specialized coding model")
            else:
                console.print("\n[bold dim]🔥 Mistral AI Models:[/bold dim]")
                console.print("  • [dim]mistral-large (unavailable)[/dim]")
                console.print("  • [dim]mistral-medium (unavailable)[/dim]")
                console.print("  • [dim]mistral-small (unavailable)[/dim]")
                console.print("  • [dim]mistral-codestral (unavailable)[/dim]")

            # Show current model and performance metrics
            current_model = agent_state.model_context['current_model']
            metrics = performance_optimizer.get_performance_metrics()

            console.print(f"\n[bold white]📊 Current Status:[/bold white]")
            console.print(f"  • Active Model: [bright_green]{current_model}[/bright_green]")
            console.print(f"  • Cache Hit Rate: [bright_blue]{metrics['cache_hit_rate']:.1%}[/bright_blue]")
            console.print(f"  • Avg Response Time: [bright_yellow]{metrics['avg_response_time']:.2f}s[/bright_yellow]")

            console.print("\n[dim]Usage: /switch <model-name>[/dim]")
            console.print("[dim]Example: /switch mistral-large[/dim]")
            return True

        model_name = parts[1].lower()

        # Enhanced model switching with validation
        model_switched = False

        # DeepSeek models
        if model_name in ["deepseek", "deepseek-chat"]:
            agent_state.model_context['current_model'] = "deepseek-chat"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to DeepSeek Chat model 💬[/green]")
            model_switched = True

        elif model_name in ["reasoner", "deepseek-reasoner"]:
            agent_state.model_context['current_model'] = "deepseek-reasoner"
            agent_state.model_context['is_reasoner'] = True
            console.print("[green]✓ Switched to DeepSeek Reasoner model 🧠[/green]")
            model_switched = True

        # Gemini models
        elif model_name in ["gemini", "gemini-2.0-flash"] and GEMINI_AVAILABLE:
            agent_state.model_context['current_model'] = "gemini-2.0-flash"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to Gemini 2.0 Flash model 🌟[/green]")
            model_switched = True

        elif model_name in ["gemini-1.5", "gemini-1.5-flash"] and GEMINI_AVAILABLE:
            agent_state.model_context['current_model'] = "gemini-1.5-flash"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to Gemini 1.5 Flash model ⚡[/green]")
            model_switched = True

        # Mistral models
        elif model_name in ["mistral-large", "mistral"] and MISTRAL_AVAILABLE:
            agent_state.model_context['current_model'] = "mistral-large"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to Mistral Large model 🔥[/green]")
            model_switched = True

        elif model_name in ["mistral-medium"] and MISTRAL_AVAILABLE:
            agent_state.model_context['current_model'] = "mistral-medium"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to Mistral Medium model 🚀[/green]")
            model_switched = True

        elif model_name in ["mistral-small"] and MISTRAL_AVAILABLE:
            agent_state.model_context['current_model'] = "mistral-small"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to Mistral Small model ⚡[/green]")
            model_switched = True

        elif model_name in ["mistral-codestral", "codestral"] and MISTRAL_AVAILABLE:
            agent_state.model_context['current_model'] = "mistral-codestral"
            agent_state.model_context['is_reasoner'] = False
            console.print("[green]✓ Switched to Mistral Codestral model 💻[/green]")
            model_switched = True

        # Handle unavailable models
        elif model_name.startswith("gemini") and not GEMINI_AVAILABLE:
            console.print(f"[red]✗ Gemini models are not available[/red]")
            console.print("[dim]Check your Gemini API key configuration[/dim]")

        elif model_name.startswith("mistral") and not MISTRAL_AVAILABLE:
            console.print(f"[red]✗ Mistral models are not available[/red]")
            console.print("[dim]Check your Mistral API key configuration[/dim]")

        else:
            console.print(f"[red]✗ Unknown model: {model_name}[/red]")
            console.print("[dim]Use /switch to see available models[/dim]")

        # Show performance hint for successful switches
        if model_switched:
            console.print("[dim]💡 Tip: The agent will automatically optimize performance for this model[/dim]")

        return True
    return False

def try_handle_clear_command(user_input: str) -> bool:
    """Handle /clear command to clear screen."""
    if user_input.strip().lower() == "/clear":
        console.clear()
        return True
    return False

def try_handle_clear_context_command(user_input: str) -> bool:
    """Handle /clear-context command to clear conversation history."""
    if user_input.strip().lower() == "/clear-context":
        if len(conversation_history) <= 1:
            console.print("[yellow]Context already empty (only system prompt).[/yellow]")
            return True
            
        # Show current context size
        file_contexts = sum(1 for msg in conversation_history if msg["role"] == "system" and "User added file" in msg["content"])
        total_messages = len(conversation_history) - 1  # Exclude system prompt
        
        console.print(f"[yellow]Current context: {total_messages} messages, {file_contexts} file contexts[/yellow]")
        
        # Ask for confirmation since this is destructive
        confirm = prompt_session.prompt("🔵 Clear conversation context? This cannot be undone (y/n): ").strip().lower()
        if confirm in ["y", "yes"]:
            # Keep only the original system prompt
            original_system_prompt = conversation_history[0]
            conversation_history[:] = [original_system_prompt]
            console.print("[green]✓ Conversation context cleared. Starting fresh![/green]")
            console.print("[green]  All file contexts and conversation history removed.[/green]")
        else:
            console.print("[yellow]Context clear cancelled.[/yellow]")
        return True
    return False

def try_handle_folder_command(user_input: str) -> bool:
    """Handle /folder command to manage base directory."""
    global base_dir
    if user_input.strip().lower().startswith("/folder"):
        folder_path = user_input[len("/folder"):].strip()
        if not folder_path:
            console.print(f"[yellow]Current base directory: '{base_dir}'[/yellow]")
            console.print("[yellow]Usage: /folder <path> or /folder reset[/yellow]")
            return True
        if folder_path.lower() == "reset":
            old_base = base_dir
            base_dir = Path.cwd()
            console.print(f"[green]✓ Base directory reset from '{old_base}' to: '{base_dir}'[/green]")
            return True
        try:
            new_base = Path(folder_path).resolve()
            if not new_base.exists() or not new_base.is_dir():
                console.print(f"[red]✗ Path does not exist or is not a directory: '{folder_path}'[/red]")
                return True
            # Check write permissions
            test_file = new_base / ".eng-git-test"
            try:
                test_file.touch()
                test_file.unlink()
            except PermissionError:
                console.print(f"[red]✗ No write permissions in directory: '{new_base}'[/red]")
                return True
            old_base = base_dir
            base_dir = new_base
            console.print(f"[green]✓ Base directory changed from '{old_base}' to: '{base_dir}'[/green]")
            console.print(f"[green]  All relative paths will now be resolved against this directory.[/green]")
            return True
        except Exception as e:
            console.print(f"[red]✗ Error setting base directory: {e}[/red]")
            return True
    return False

def try_handle_exit_command(user_input: str) -> bool:
    """Handle /exit and /quit commands."""
    if user_input.strip().lower() in ("/exit", "/quit"):
        console.print("[bold blue]👋 Goodbye![/bold blue]")
        sys.exit(0)
    return False

def try_handle_context_command(user_input: str) -> bool:
    """Handle /context command to show context usage statistics."""
    if user_input.strip().lower() == "/context":
        context_info = get_context_usage_info()
        
        # Create context usage table
        context_table = Table(title="📊 Context Usage Statistics", show_header=True, header_style="bold bright_blue")
        context_table.add_column("Metric", style="bright_cyan")
        context_table.add_column("Value", style="white")
        context_table.add_column("Status", style="white")
        
        # Add rows with usage information
        context_table.add_row(
            "Total Messages", 
            str(context_info["total_messages"]), 
            "📝"
        )
        context_table.add_row(
            "Estimated Tokens", 
            f"{context_info['estimated_tokens']:,}", 
            f"{context_info['token_usage_percent']:.1f}% of {ESTIMATED_MAX_TOKENS:,}"
        )
        context_table.add_row(
            "File Contexts", 
            str(context_info["file_contexts"]), 
            f"Max: {MAX_CONTEXT_FILES}"
        )
        
        # Status indicators
        if context_info["critical_limit"]:
            status_color = "red"
            status_text = "🔴 Critical - aggressive truncation active"
        elif context_info["approaching_limit"]:
            status_color = "yellow"
            status_text = "🟡 Warning - approaching limits"
        else:
            status_color = "green"
            status_text = "🟢 Healthy - plenty of space"
        
        context_table.add_row(
            "Context Health", 
            status_text, 
            ""
        )
        
        console.print(context_table)
        
        # Show token breakdown
        if context_info["token_breakdown"]:
            breakdown_table = Table(title="📋 Token Breakdown by Role", show_header=True, header_style="bold bright_blue", border_style="blue")
            breakdown_table.add_column("Role", style="bright_cyan")
            breakdown_table.add_column("Tokens", style="white")
            breakdown_table.add_column("Percentage", style="white")
            
            total_tokens = context_info["estimated_tokens"]
            for role, tokens in context_info["token_breakdown"].items():
                if tokens > 0:
                    percentage = (tokens / total_tokens * 100) if total_tokens > 0 else 0
                    breakdown_table.add_row(
                        role.capitalize(),
                        f"{tokens:,}",
                        f"{percentage:.1f}%"
                    )
            
            console.print(breakdown_table)
        
        # Show recommendations if approaching limits
        if context_info["approaching_limit"]:
            console.print("\n[yellow]💡 Recommendations to manage context:[/yellow]")
            console.print("[yellow]  • Use /clear-context to start fresh[/yellow]")
            console.print("[yellow]  • Remove large files from context[/yellow]")
            console.print("[yellow]  • Work with smaller file sections[/yellow]")
        
        return True
    return False

def try_handle_help_command(user_input: str) -> bool:
    """Handle /help command to show available commands."""
    if user_input.strip().lower() == "/help":
        help_table = Table(title="📝 Available Commands", show_header=True, header_style="bold bright_blue")
        help_table.add_column("Command", style="bright_cyan")
        help_table.add_column("Description", style="white")
        
        # General commands
        help_table.add_row("/help", "Show this help")
        help_table.add_row("/r1", "Call DeepSeek Reasoner model for one-off reasoning tasks")
        help_table.add_row("/reasoner", "Toggle between chat and reasoner models")
        help_table.add_row("/switch <model>", "Switch between models (DeepSeek/Gemini/Mistral)")
        help_table.add_row("/models", "Show model health and performance status")
        help_table.add_row("/performance", "Show enhanced performance metrics")
        help_table.add_row("/cache", "Show cache statistics and management")
        help_table.add_row("/optimize", "Auto-optimize performance settings")
        help_table.add_row("/mcp", "Show MCP (Model Context Protocol) commands")
        help_table.add_row("/mcp config", "Open MCP configuration in Notepad")
        help_table.add_row("/mcp status", "Show MCP server status dashboard")
        help_table.add_row("/clear", "Clear screen")
        help_table.add_row("/clear-context", "Clear conversation context")
        help_table.add_row("/context", "Show context usage statistics")
        help_table.add_row("/exit, /quit", "Exit application")
        
        # Directory & file management
        help_table.add_row("/folder", "Show current base directory")
        help_table.add_row("/folder <path>", "Set base directory for file operations")
        help_table.add_row("/folder reset", "Reset base directory to current working directory")
        help_table.add_row(f"{ADD_COMMAND_PREFIX.strip()} <path>", "Add file/dir to conversation context (supports fuzzy matching)")
        
        # Git workflow commands
        help_table.add_row("/git init", "Initialize Git repository")
        help_table.add_row("/git status", "Show Git status")
        help_table.add_row(f"{GIT_BRANCH_COMMAND_PREFIX.strip()} <name>", "Create & switch to new branch")
        help_table.add_row(f"{COMMIT_COMMAND_PREFIX.strip()} [msg]", "Stage all files & commit (prompts if no message)")
        help_table.add_row("/git-info", "Show detailed Git capabilities")

        # Enhanced commands (if available)
        if ADVANCED_MODULES_AVAILABLE:
            help_table.add_row("", "")  # Separator
            help_table.add_row("[bold]Enhanced Features:[/bold]", "")
            help_table.add_row("/status", "Show comprehensive system status")
            help_table.add_row("/performance", "Show performance metrics")
            help_table.add_row("/codebase", "Show codebase summary")
            help_table.add_row("/intelligence", "Show AI intelligence metrics")

        console.print(help_table)
        
        # Show current model status
        current_model_name = "DeepSeek Reasoner 🧠" if agent_state.model_context['is_reasoner'] else "DeepSeek Chat 💬"
        console.print(f"\n[dim]Current model: {current_model_name}[/dim]")
        
        # Show fuzzy matching status
        fuzzy_status = "✓ Available" if FUZZY_AVAILABLE else "✗ Not installed (pip install thefuzz python-levenshtein)"
        console.print(f"[dim]Fuzzy matching: {fuzzy_status}[/dim]")

        # Show comprehensive function tools overview
        console.print("\n[bold bright_green]🛠️ Available Function Tools (50+ Advanced Functions):[/bold bright_green]")

        # File Operations
        console.print("\n[bold cyan]📁 FILE OPERATIONS:[/bold cyan]")
        file_ops = [
            "read_file(path) - Read single file content",
            "read_multiple_files(paths) - Read multiple files efficiently",
            "create_file(path, content) - Create/overwrite files",
            "create_multiple_files(files) - Create multiple files at once",
            "edit_file(path, old_snippet, new_snippet) - Precise edits with fuzzy matching"
        ]
        for op in file_ops:
            console.print(f"  • {op}")

        # Advanced Search & Pattern Matching
        console.print("\n[bold cyan]🔍 ADVANCED SEARCH & PATTERN MATCHING:[/bold cyan]")
        search_ops = [
            "advanced_pattern_search(query, mode, file_types) - Semantic/fuzzy/regex/hybrid search",
            "smart_file_search(query, type, patterns) - Advanced grep++ with context",
            "search_code(pattern, type, files) - Basic code pattern search",
            "search_file_index(query, type, max_results) - Search indexed codebase"
        ]
        for op in search_ops:
            console.print(f"  • {op}")

        # Code Replacement & Refactoring
        console.print("\n[bold cyan]🔧 CODE REPLACEMENT & REFACTORING:[/bold cyan]")
        refactor_ops = [
            "smart_replace_code(pattern, replacement, mode) - Intelligent replacement with Git",
            "refactor_code(file, type, target) - Automated refactoring",
            "fix_code_input(code, language, fix_types) - Fix malformed code"
        ]
        for op in refactor_ops:
            console.print(f"  • {op}")

        # Autonomous Planning & Execution
        console.print("\n[bold cyan]🤖 AUTONOMOUS PLANNING & EXECUTION:[/bold cyan]")
        planning_ops = [
            "create_execution_plan(request, strategy, auto_execute) - Create autonomous plans",
            "execute_plan(plan_id, auto_commit) - Execute created plans",
            "list_execution_plans() - Show all saved plans"
        ]
        for op in planning_ops:
            console.print(f"  • {op}")

        # Code Analysis & Quality
        console.print("\n[bold cyan]🔬 CODE ANALYSIS & QUALITY:[/bold cyan]")
        analysis_ops = [
            "analyze_code(file, analysis_type) - Deep code analysis with AST",
            "debug_code(file, error, auto_fix) - Autonomous debugging",
            "generate_tests(file, framework, coverage) - Generate unit tests"
        ]
        for op in analysis_ops:
            console.print(f"  • {op}")

        # File Indexing & Codebase Intelligence
        console.print("\n[bold cyan]📚 FILE INDEXING & CODEBASE INTELLIGENCE:[/bold cyan]")
        index_ops = [
            "build_file_index(force_rebuild) - Build semantic codebase index",
            "get_index_stats() - Get indexing statistics"
        ]
        for op in index_ops:
            console.print(f"  • {op}")

        # Git Operations
        console.print("\n[bold cyan]📊 GIT OPERATIONS:[/bold cyan]")
        git_ops = [
            "git_init() - Initialize Git repository",
            "git_status() - Show Git status",
            "git_add(files) - Stage files for commit",
            "git_commit(message) - Commit with message",
            "git_create_branch(name) - Create and switch to branch"
        ]
        for op in git_ops:
            console.print(f"  • {op}")

        # Terminal & System Integration
        console.print("\n[bold cyan]🖥️ TERMINAL & SYSTEM INTEGRATION:[/bold cyan]")
        terminal_ops = [
            "run_terminal_command(command, dir, timeout) - Execute terminal commands",
            "run_powershell(command) - Windows PowerShell execution"
        ]
        for op in terminal_ops:
            console.print(f"  • {op}")

        # Web Search & Knowledge
        console.print("\n[bold cyan]🌐 WEB SEARCH & KNOWLEDGE:[/bold cyan]")
        web_ops = [
            "web_search(query, max_results, type) - Search for programming help",
            "show_advanced_tools_help(tool_name) - Get comprehensive help"
        ]
        for op in web_ops:
            console.print(f"  • {op}")

        # Code Conversion
        console.print("\n[bold cyan]🔄 CODE CONVERSION:[/bold cyan]")
        convert_ops = [
            "convert_code(source_file, target_language, preserve_comments) - Language conversion"
        ]
        for op in convert_ops:
            console.print(f"  • {op}")

        console.print(f"\n[bold bright_yellow]💡 NATURAL LANGUAGE EXAMPLES:[/bold bright_yellow]")
        examples = [
            "'Find all authentication-related code in my project'",
            "'Create a comprehensive plan for adding JWT authentication'",
            "'Fix all syntax errors in my Python files'",
            "'Search for database connection patterns using semantic search'",
            "'Refactor my authentication functions safely with preview'",
            "'Generate tests for my API endpoints with 95% coverage'",
            "'Build an index of my codebase for better navigation'",
            "'Show me how to use the advanced pattern search tool'",
            "'Create a plan to optimize my application performance'",
            "'Find and replace all deprecated API calls across my project'"
        ]

        for example in examples:
            console.print(f"  • {example}")

        console.print(f"\n[bold bright_magenta]🎯 WORKFLOW EXAMPLES:[/bold bright_magenta]")
        workflows = [
            "1. build_file_index() → advanced_pattern_search() → smart_replace_code()",
            "2. create_execution_plan() → execute_plan() → generate_tests()",
            "3. smart_file_search() → debug_code() → fix_code_input()",
            "4. analyze_code() → refactor_code() → git_commit()",
            "5. web_search() → create_file() → run_terminal_command()"
        ]

        for workflow in workflows:
            console.print(f"  • {workflow}")

        console.print(f"\n[dim]🚀 CODY has 50+ advanced functions with complete context and examples![/dim]")
        console.print(f"[dim]💡 Use show_advanced_tools_help('all') for detailed documentation[/dim]")

        return True
    return False

async def try_handle_mcp_commands(user_input: str) -> bool:
    """Handle MCP-related commands."""
    user_input = user_input.strip().lower()

    if user_input == "/mcp":
        show_mcp_help()
        return True

    elif user_input == "/mcp config":
        mcp_client.open_config_in_notepad()
        return True

    elif user_input == "/mcp reload":
        mcp_client.load_config()
        console.print("[green]✅ MCP configuration reloaded[/green]")
        return True

    elif user_input == "/mcp list":
        show_mcp_servers()
        return True

    elif user_input == "/mcp status":
        show_mcp_status()
        return True

    elif user_input == "/mcp start":
        await mcp_client.start_all_servers()
        return True

    elif user_input == "/mcp stop":
        await mcp_client.stop_all_servers()
        return True

    elif user_input.startswith("/mcp start "):
        server_name = user_input.replace("/mcp start ", "").strip()
        await mcp_client.start_server(server_name)
        return True

    elif user_input.startswith("/mcp stop "):
        server_name = user_input.replace("/mcp stop ", "").strip()
        await mcp_client.stop_server(server_name)
        return True

    elif user_input.startswith("/mcp test "):
        server_name = user_input.replace("/mcp test ", "").strip()
        await mcp_client.test_server(server_name)
        return True

    return False

def show_mcp_help():
    """Show MCP command help."""
    console.print("\n[bold bright_blue]🔌 MCP (Model Context Protocol) Commands[/bold bright_blue]")

    mcp_table = Table(title="MCP Commands", show_header=True, header_style="bold cyan")
    mcp_table.add_column("Command", style="cyan", width=25)
    mcp_table.add_column("Description", style="white", width=50)

    mcp_table.add_row("/mcp", "Show this help message")
    mcp_table.add_row("/mcp config", "Open MCP configuration in Notepad")
    mcp_table.add_row("/mcp reload", "Reload MCP configuration from file")
    mcp_table.add_row("/mcp list", "List all configured MCP servers")
    mcp_table.add_row("/mcp status", "Show status of all MCP servers")
    mcp_table.add_row("/mcp start", "Start all MCP servers")
    mcp_table.add_row("/mcp stop", "Stop all MCP servers")
    mcp_table.add_row("/mcp start <name>", "Start specific MCP server")
    mcp_table.add_row("/mcp stop <name>", "Stop specific MCP server")
    mcp_table.add_row("/mcp test <name>", "Test specific MCP server connection")

    console.print(mcp_table)

    console.print("\n[bold yellow]💡 Getting Started:[/bold yellow]")
    console.print("1. Use '/mcp config' to open configuration file")
    console.print("2. Configure your MCP servers in the JSON file")
    console.print("3. Use '/mcp start' to start all servers")
    console.print("4. Use '/mcp status' to check server health")

def show_mcp_servers():
    """Show list of configured MCP servers."""
    console.print("\n[bold bright_blue]🔌 Configured MCP Servers[/bold bright_blue]")

    if not mcp_client.servers:
        console.print("[yellow]No MCP servers configured. Use '/mcp config' to add servers.[/yellow]")
        return

    servers_table = Table(title="MCP Server Configuration", show_header=True, header_style="bold cyan")
    servers_table.add_column("Name", style="cyan", width=20)
    servers_table.add_column("Command", style="green", width=15)
    servers_table.add_column("Arguments", style="yellow", width=40)
    servers_table.add_column("Status", style="white", width=15)

    for name, server in mcp_client.servers.items():
        status_emoji = {
            "connected": "🟢 Connected",
            "disconnected": "🔴 Disconnected",
            "error": "❌ Error"
        }.get(server.status, "❓ Unknown")

        args_str = " ".join(server.args)
        if len(args_str) > 35:
            args_str = args_str[:32] + "..."

        servers_table.add_row(name, server.command, args_str, status_emoji)

    console.print(servers_table)

def show_mcp_status():
    """Show detailed status of all MCP servers."""
    console.print("\n[bold bright_blue]📊 MCP Server Status Dashboard[/bold bright_blue]")

    status = mcp_client.get_server_status()

    if not status:
        console.print("[yellow]No MCP servers configured.[/yellow]")
        return

    status_table = Table(title="Server Health Status", show_header=True, header_style="bold cyan")
    status_table.add_column("Server", style="cyan", width=20)
    status_table.add_column("Status", style="green", width=15)
    status_table.add_column("Last Error", style="red", width=40)
    status_table.add_column("Actions", style="blue", width=20)

    connected_count = 0
    error_count = 0

    for name, server_status in status.items():
        status_text = server_status["status"]
        status_emoji = {
            "connected": "🟢 Connected",
            "disconnected": "🔴 Disconnected",
            "error": "❌ Error"
        }.get(status_text, "❓ Unknown")

        if status_text == "connected":
            connected_count += 1
        elif status_text == "error":
            error_count += 1

        last_error = server_status.get("last_error", "None")
        if last_error and len(last_error) > 35:
            last_error = last_error[:32] + "..."

        actions = "✅ Ready" if status_text == "connected" else "🔄 Start needed"

        status_table.add_row(name, status_emoji, last_error or "None", actions)

    console.print(status_table)

    # Summary
    total_servers = len(status)
    console.print(f"\n[bold white]📈 Summary:[/bold white]")
    console.print(f"  • Total Servers: {total_servers}")
    console.print(f"  • Connected: [green]{connected_count}[/green]")
    console.print(f"  • Disconnected: [yellow]{total_servers - connected_count - error_count}[/yellow]")
    console.print(f"  • Errors: [red]{error_count}[/red]")

    if error_count > 0:
        console.print("\n[bold yellow]💡 Troubleshooting:[/bold yellow]")
        console.print("  • Check if required packages are installed (uvx, npx)")
        console.print("  • Verify server commands and arguments in config")
        console.print("  • Use '/mcp test <server>' to diagnose specific issues")

def initialize_git_repo_cmd() -> bool:
    """Initialize a git repository."""
    if Path(".git").exists(): 
        console.print("[yellow]Git repo already exists.[/yellow]")
        agent_state.git_context['enabled'] = True
        return True
    try:
        subprocess.run(["git", "init"], cwd=str(Path.cwd()), check=True, capture_output=True)
        agent_state.git_context['enabled'] = True
        branch_res = subprocess.run(["git", "rev-parse", "--abbrev-ref", "HEAD"], cwd=str(Path.cwd()), capture_output=True, text=True)
        agent_state.git_context['branch'] = branch_res.stdout.strip() if branch_res.returncode == 0 else "main"
        console.print(f"[green]✓ Initialized Git repo in {Path.cwd()}/.git/ (branch: {agent_state.git_context['branch']})[/green]")
        if not Path(".gitignore").exists() and prompt_session.prompt("🔵 No .gitignore. Create one? (y/n, default y): ", default="y").strip().lower() in ["y", "yes"]: 
            create_gitignore()
        elif agent_state.git_context['enabled'] and Path(".gitignore").exists(): 
            stage_file(".gitignore")
        if prompt_session.prompt(f"🔵 Initial commit? (y/n, default n): ", default="n").strip().lower() in ["y", "yes"]: 
            user_commit_changes("Initial commit")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e: 
        console.print(f"[red]✗ Failed to init Git: {e}[/red]")
        if isinstance(e, FileNotFoundError): 
            agent_state.git_context['enabled'] = False
        return False

def create_git_branch_cmd(branch_name: str) -> bool:
    """Create and switch to a git branch."""
    if not agent_state.git_context['enabled']: 
        console.print("[yellow]Git not enabled.[/yellow]")
        return True
    if not branch_name: 
        console.print("[yellow]Branch name empty.[/yellow]")
        return True
    try:
        existing_raw = subprocess.run(["git", "branch", "--list", branch_name], cwd=str(Path.cwd()), capture_output=True, text=True)
        if existing_raw.stdout.strip():
            console.print(f"[yellow]Branch '{branch_name}' exists.[/yellow]")
            current_raw = subprocess.run(["git", "branch", "--show-current"], cwd=str(Path.cwd()), capture_output=True, text=True)
            if current_raw.stdout.strip() != branch_name and prompt_session.prompt(f"🔵 Switch to '{branch_name}'? (y/n, default y): ", default="y").strip().lower() in ["y", "yes"]:
                subprocess.run(["git", "checkout", branch_name], cwd=str(Path.cwd()), check=True, capture_output=True)
                agent_state.git_context['branch'] = branch_name
                console.print(f"[green]✓ Switched to branch '{branch_name}'[/green]")
            return True
        subprocess.run(["git", "checkout", "-b", branch_name], cwd=str(Path.cwd()), check=True, capture_output=True)
        agent_state.git_context['branch'] = branch_name
        console.print(f"[green]✓ Created & switched to new branch '{branch_name}'[/green]")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e: 
        console.print(f"[red]✗ Branch op failed: {e}[/red]")
        if isinstance(e, FileNotFoundError): 
            agent_state.git_context['enabled'] = False
        return False

def show_git_status_cmd() -> bool:
    """Show git status."""
    if not agent_state.git_context['enabled']: 
        console.print("[yellow]Git not enabled.[/yellow]")
        return True
    has_changes, files = get_git_status_porcelain()
    branch_raw = subprocess.run(["git", "branch", "--show-current"], cwd=str(Path.cwd()), capture_output=True, text=True)
    branch_msg = f"On branch {branch_raw.stdout.strip()}" if branch_raw.returncode == 0 and branch_raw.stdout.strip() else "Not on any branch?"
    console.print(Panel(branch_msg, title="Git Status", border_style="blue", expand=False))
    if not has_changes: 
        console.print("[green]Working tree clean.[/green]")
        return True
    table = Table(show_header=True, header_style="bold bright_blue", border_style="blue")
    table.add_column("Sts", width=3)
    table.add_column("File Path")
    table.add_column("Description", style="dim")
    s_map = {
        " M": (" M", "Mod (unstaged)"), "MM": ("MM", "Mod (staged&un)"), 
        " A": (" A", "Add (unstaged)"), "AM": ("AM", "Add (staged&mod)"), 
        "AD": ("AD", "Add (staged&del)"), " D": (" D", "Del (unstaged)"), 
        "??": ("??", "Untracked"), "M ": ("M ", "Mod (staged)"), 
        "A ": ("A ", "Add (staged)"), "D ": ("D ", "Del (staged)"), 
        "R ": ("R ", "Ren (staged)"), "C ": ("C ", "Cop (staged)"), 
        "U ": ("U ", "Unmerged")
    }
    staged, unstaged, untracked = False, False, False
    for code, filename in files:
        disp_code, desc = s_map.get(code, (code, "Unknown"))
        table.add_row(disp_code, filename, desc)
        if code == "??": 
            untracked = True
        elif code.startswith(" "): 
            unstaged = True
        else: 
            staged = True
    console.print(table)
    if not staged and (unstaged or untracked): 
        console.print("\n[yellow]No changes added to commit.[/yellow]")
    if staged: 
        console.print("\n[green]Changes to be committed.[/green]")
    if unstaged: 
        console.print("[yellow]Changes not staged for commit.[/yellow]")
    if untracked: 
        console.print("[cyan]Untracked files present.[/cyan]")
    return True

# -----------------------------------------------------------------------------
# 10. ENHANCED LLM TOOL HANDLER FUNCTIONS
# -----------------------------------------------------------------------------

def llm_git_init() -> str:
    """LLM tool handler for git init."""
    if Path(".git").exists(): 
        agent_state.git_context['enabled'] = True
        return "Git repository already exists."
    try:
        subprocess.run(["git", "init"], cwd=str(Path.cwd()), check=True, capture_output=True)
        agent_state.git_context['enabled'] = True
        branch_res = subprocess.run(["git", "rev-parse", "--abbrev-ref", "HEAD"], cwd=str(Path.cwd()), capture_output=True, text=True)
        agent_state.git_context['branch'] = branch_res.stdout.strip() if branch_res.returncode == 0 else "main"
        if not Path(".gitignore").exists(): 
            create_gitignore()
        elif agent_state.git_context['enabled']: 
            stage_file(".gitignore")
        return f"Git repository initialized successfully in {Path.cwd()}/.git/ (branch: {agent_state.git_context['branch']})."

    except (subprocess.CalledProcessError, FileNotFoundError) as e: 
        if isinstance(e, FileNotFoundError):
            agent_state.git_context['enabled'] = False
        return f"Failed to initialize Git repository: {e}"

def llm_git_add(file_paths: List[str]) -> str:
    """LLM tool handler for git add."""
    if not agent_state.git_context['enabled']: 
        return "Git not initialized."
    if not file_paths: 
        return "No file paths to stage."
    staged_ok: List[str] = []
    failed_stage: List[str] = []
    for fp_str in file_paths:
        try: 
            norm_fp = normalize_path(fp_str)
            if stage_file(norm_fp):
                staged_ok.append(norm_fp)
            else:
                failed_stage.append(norm_fp)
        except ValueError as e: 
            failed_stage.append(f"{fp_str} (path error: {e})")
        except Exception as e: 
            failed_stage.append(f"{fp_str} (error: {e})")
    res = []
    if staged_ok: 
        res.append(f"Staged: {', '.join(Path(p).name for p in staged_ok)}")
    if failed_stage: 
        res.append(f"Failed to stage: {', '.join(str(Path(p).name if isinstance(p,str) else p) for p in failed_stage)}")
    return ". ".join(res) + "." if res else "No files staged. Check paths."

def llm_git_commit(message: str) -> str:
    """LLM tool handler for git commit."""
    if not agent_state.git_context['enabled']: 
        return "Git not initialized."
    if not message: 
        return "Commit message empty."
    try:
        staged_check = subprocess.run(["git", "diff", "--staged", "--quiet"], cwd=str(Path.cwd()))
        if staged_check.returncode == 0: 
            return "No changes staged. Use git_add first."
        result = subprocess.run(["git", "commit", "-m", message], cwd=str(Path.cwd()), capture_output=True, text=True)
        if result.returncode == 0:
            info_raw = subprocess.run(["git", "log", "-1", "--pretty=%h %s"], cwd=str(Path.cwd()), capture_output=True, text=True).stdout.strip()
            return f"Committed. Commit: {info_raw}"
        return f"Failed to commit: {result.stderr.strip()}"
    except (subprocess.CalledProcessError, FileNotFoundError) as e: 
        if isinstance(e, FileNotFoundError):
            agent_state.git_context['enabled'] = False
        return f"Git commit error: {e}"
    except Exception as e: 
        console.print_exception()
        return f"Unexpected commit error: {e}"

def llm_git_create_branch(branch_name: str) -> str:
    """LLM tool handler for git branch creation."""
    if not agent_state.git_context['enabled']: 
        return "Git not initialized."
    bn = branch_name.strip()
    if not bn: 
        return "Branch name empty."
    try:
        exist_res = subprocess.run(["git", "rev-parse", "--verify", f"refs/heads/{bn}"], cwd=str(Path.cwd()), capture_output=True, text=True)
        if exist_res.returncode == 0:
            current_raw = subprocess.run(["git", "branch", "--show-current"], cwd=str(Path.cwd()), capture_output=True, text=True)
            if current_raw.stdout.strip() == bn: 
                return f"Already on branch '{bn}'."
            subprocess.run(["git", "checkout", bn], cwd=str(Path.cwd()), check=True, capture_output=True, text=True)
            agent_state.git_context['branch'] = bn
            return f"Branch '{bn}' exists. Switched to it."
        subprocess.run(["git", "checkout", "-b", bn], cwd=str(Path.cwd()), check=True, capture_output=True, text=True)
        agent_state.git_context['branch'] = bn
        return f"Created & switched to new branch '{bn}'."
    except (subprocess.CalledProcessError, FileNotFoundError) as e: 
        if isinstance(e, FileNotFoundError):
            agent_state.git_context['enabled'] = False
        return f"Branch op failed for '{bn}': {e}"

def llm_git_status() -> str:
    """LLM tool handler for git status."""
    if not agent_state.git_context['enabled']: 
        return "Git not initialized."
    try:
        branch_res = subprocess.run(["git", "branch", "--show-current"], cwd=str(Path.cwd()), capture_output=True, text=True)
        branch_name = branch_res.stdout.strip() if branch_res.returncode == 0 and branch_res.stdout.strip() else "detached HEAD"
        has_changes, files = get_git_status_porcelain()
        if not has_changes: 
            return f"On branch '{branch_name}'. Working tree clean."
        lines = [f"On branch '{branch_name}'."]
        staged: List[str] = []
        unstaged: List[str] = []
        untracked: List[str] = []
        for code, filename in files:
            if code == "??": 
                untracked.append(filename)
            elif code.startswith(" "): 
                unstaged.append(f"{code.strip()} {filename}")
            else: 
                staged.append(f"{code.strip()} {filename}")
        if staged: 
            lines.extend(["\nChanges to be committed:"] + [f"  {s}" for s in staged])
        if unstaged: 
            lines.extend(["\nChanges not staged for commit:"] + [f"  {s}" for s in unstaged])
        if untracked: 
            lines.extend(["\nUntracked files:"] + [f"  {f}" for f in untracked])
        return "\n".join(lines)
    except (subprocess.CalledProcessError, FileNotFoundError) as e: 
        if isinstance(e, FileNotFoundError):
            agent_state.git_context['enabled'] = False
        return f"Git status error: {e}"

def execute_function_call_dict(tool_call_dict: Dict[str, Any]) -> str:
    """
    Execute a function call from the LLM with enhanced fuzzy matching and security.
    
    Args:
        tool_call_dict: Dictionary containing function call information
        
    Returns:
        String result of the function execution
    """
    func_name = "unknown_function"
    try:
        func_name = tool_call_dict["function"]["name"]
        args = json.loads(tool_call_dict["function"]["arguments"])
        
        if func_name == "read_file":
            norm_path = normalize_path(args["file_path"])
            content = read_local_file(norm_path)
            return f"Content of file '{norm_path}':\n\n{content}"
            
        elif func_name == "read_multiple_files":
            response_data = {
                "files_read": {},
                "errors": {}
            }
            total_content_size = 0

            for fp in args["file_paths"]:
                try:
                    norm_path = normalize_path(fp)
                    content = read_local_file(norm_path)

                    if total_content_size + len(content) > MAX_MULTIPLE_READ_SIZE:
                        response_data["errors"][norm_path] = "Could not read file, as total content size would exceed the safety limit."
                        continue

                    response_data["files_read"][norm_path] = content
                    total_content_size += len(content)

                except (OSError, ValueError) as e:
                    # Use the original path in the error if normalization fails
                    error_key = str(base_dir / fp)
                    response_data["errors"][error_key] = str(e)

            # Return a JSON string, which is much easier for the LLM to parse reliably
            return json.dumps(response_data, indent=2)
            
        elif func_name == "create_file": 
            create_file(args["file_path"], args["content"])
            return f"File '{args['file_path']}' created/updated."
            
        elif func_name == "create_multiple_files":
            created: List[str] = []
            errors: List[str] = []
            for f_info in args["files"]:
                try: 
                    create_file(f_info["path"], f_info["content"])
                    created.append(f_info["path"])
                except Exception as e: 
                    errors.append(f"Error creating {f_info.get('path','?path')}: {e}")
            res_parts = []
            if created: 
                res_parts.append(f"Created/updated {len(created)} files: {', '.join(created)}")
            if errors: 
                res_parts.append(f"Errors: {'; '.join(errors)}")
            return ". ".join(res_parts) if res_parts else "No files processed."
            
        elif func_name == "edit_file":
            fp = args["file_path"]
            if not ensure_file_in_context(fp): 
                return f"Error: Could not read '{fp}' for editing."
            try: 
                apply_fuzzy_diff_edit(fp, args["original_snippet"], args["new_snippet"])
                return f"Edit applied successfully to '{fp}'. Check console for details."
            except Exception as e:
                return f"Error during edit_file call for '{fp}': {e}."
                
        elif func_name == "git_init": 
            return llm_git_init()
        elif func_name == "git_add": 
            return llm_git_add(args.get("file_paths", []))
        elif func_name == "git_commit": 
            return llm_git_commit(args.get("message", "Auto commit"))
        elif func_name == "git_create_branch": 
            return llm_git_create_branch(args.get("branch_name", ""))
        elif func_name == "git_status": 
            return llm_git_status()
        elif func_name == "run_powershell":
            command = args["command"]
            
            # SECURITY GATE
            if agent_state.security_context["require_powershell_confirmation"]:
                console.print(Panel(
                    f"The assistant wants to run this PowerShell command:\n\n[bold yellow]{command}[/bold yellow]", 
                    title="🚨 Security Confirmation Required", 
                    border_style="red"
                ))
                confirm = prompt_session.prompt("🔵 Do you want to allow this command to run? (y/N): ", default="n").strip().lower()
                
                if confirm not in ["y", "yes"]:
                    console.print("[red]Execution denied by user.[/red]")
                    return "PowerShell command execution was denied by the user."
            
            output, error = run_powershell_command(command)
            if error:
                return f"PowerShell Error:\n{error}"
            return f"PowerShell Output:\n{output}"

        # New advanced function implementations
        elif func_name == "analyze_code":
            if not ADVANCED_MODULES_AVAILABLE or not code_analyzer:
                return "Code analysis not available - advanced modules not loaded"

            file_path = args["file_path"]
            analysis_type = args.get("analysis_type", "all")

            try:
                result = code_analyzer.analyze_file(file_path, analysis_type)
                return json.dumps(result, indent=2, default=str)
            except Exception as e:
                return f"Code analysis failed: {e}"

        elif func_name == "search_code":
            if not ADVANCED_MODULES_AVAILABLE or not code_analyzer:
                return "Code search not available - advanced modules not loaded"

            pattern = args["pattern"]
            search_type = args.get("search_type", "regex")
            file_paths = args.get("file_paths", [])

            try:
                results = []
                if file_paths:
                    for file_path in file_paths:
                        file_results = code_analyzer.search_code_patterns(file_path, pattern, search_type)
                        results.extend(file_results)
                else:
                    # Search in current directory
                    import glob
                    for file_path in glob.glob("**/*.py", recursive=True):
                        file_results = code_analyzer.search_code_patterns(file_path, pattern, search_type)
                        results.extend(file_results)

                return json.dumps(results, indent=2, default=str)
            except Exception as e:
                return f"Code search failed: {e}"

        elif func_name == "web_search":
            if not ADVANCED_MODULES_AVAILABLE or not web_search_rag:
                return "Web search not available - advanced modules not loaded"

            query = args["query"]
            max_results = args.get("max_results", 5)
            search_type = args.get("search_type", "general")

            try:
                results = web_search_rag.search_programming_help(query, max_results, search_type)
                summary = web_search_rag.summarize_search_results(results)
                return f"Search Results for '{query}':\n\n{summary}"
            except Exception as e:
                return f"Web search failed: {e}"

        elif func_name == "debug_code":
            if not ADVANCED_MODULES_AVAILABLE or not autonomous_debugger:
                return "Autonomous debugging not available - advanced modules not loaded"

            file_path = args["file_path"]
            error_message = args["error_message"]
            auto_fix = args.get("auto_fix", False)

            try:
                debug_result = autonomous_debugger.debug_error(file_path, error_message, auto_fix)

                response = f"Debug Analysis for '{file_path}':\n\n"
                response += f"Error Type: {debug_result.error_type}\n"
                response += f"Confidence: {debug_result.confidence:.2f}\n"
                response += f"Auto-fixable: {debug_result.auto_fixable}\n\n"
                response += "Suggested Fixes:\n"
                for i, fix in enumerate(debug_result.suggested_fixes, 1):
                    response += f"{i}. {fix}\n"

                return response
            except Exception as e:
                return f"Debug analysis failed: {e}"

        elif func_name == "refactor_code":
            return "Code refactoring functionality coming soon..."

        elif func_name == "generate_tests":
            return "Test generation functionality coming soon..."

        elif func_name == "run_terminal_command":
            command = args["command"]
            working_directory = args.get("working_directory", str(base_dir))
            timeout = args.get("timeout", 30)

            try:
                # Enhanced cross-platform command execution
                import platform
                current_platform = platform.system().lower()

                # Normalize command for platform
                normalized_command = normalize_command_for_platform(command, current_platform)

                # Execute with proper encoding
                if current_platform == 'windows':
                    # Use cmd.exe for Windows
                    if not normalized_command.startswith('powershell'):
                        normalized_command = f'cmd /c "{normalized_command}"'

                result = subprocess.run(
                    normalized_command,
                    shell=True,
                    cwd=working_directory,
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    encoding='utf-8',
                    errors='ignore'
                )

                response = f"Command: {command}\n"
                response += f"Normalized: {normalized_command}\n"
                response += f"Exit Code: {result.returncode}\n\n"

                if result.stdout:
                    stdout_clean = result.stdout.strip()
                    response += f"Output:\n{stdout_clean}\n"

                if result.stderr:
                    stderr_clean = result.stderr.strip()
                    response += f"Error:\n{stderr_clean}\n"

                # Add execution context
                response += f"\nWorking Directory: {working_directory}"
                response += f"\nPlatform: {current_platform}"

                return response
            except subprocess.TimeoutExpired:
                return f"Command timed out after {timeout} seconds"
            except Exception as e:
                return f"Command execution failed: {e}"

        elif func_name == "convert_code":
            return "Code conversion functionality coming soon..."

        elif func_name == "advanced_pattern_search":
            return llm_advanced_pattern_search(**args)

        elif func_name == "smart_replace_code":
            return llm_smart_replace_code(**args)

        elif func_name == "smart_file_search":
            return llm_smart_file_search(**args)

        elif func_name == "create_execution_plan":
            return llm_create_execution_plan(**args)

        elif func_name == "execute_plan":
            return llm_execute_plan(**args)

        elif func_name == "list_execution_plans":
            return llm_list_execution_plans(**args)

        elif func_name == "fix_code_input":
            return llm_fix_code_input(**args)

        elif func_name == "build_file_index":
            return llm_build_file_index(**args)

        elif func_name == "search_file_index":
            return llm_search_file_index(**args)

        elif func_name == "get_index_stats":
            return llm_get_index_stats(**args)

        elif func_name == "show_advanced_tools_help":
            return llm_show_advanced_tools_help(**args)

        else:
            return f"Unknown LLM function: {func_name}"
            
    except json.JSONDecodeError as e: 
        console.print(f"[red]JSON Decode Error for {func_name}: {e}\nArgs: {tool_call_dict.get('function',{}).get('arguments','')}[/red]")
        return f"Error: Invalid JSON args for {func_name}."
    except KeyError as e: 
        console.print(f"[red]KeyError in {func_name}: Missing key {e}[/red]")
        return f"Error: Missing param for {func_name} (KeyError: {e})."
    except Exception as e:
        console.print(f"[red]Unexpected Error in LLM func '{func_name}':[/red]")
        console.print_exception()
        return f"Unexpected error in {func_name}: {e}"

# -----------------------------------------------------------------------------
# NEW ADVANCED FUNCTION IMPLEMENTATIONS
# -----------------------------------------------------------------------------

def llm_advanced_pattern_search(query: str, search_mode: str = "hybrid",
                               file_types: Optional[List[str]] = None,
                               context_lines: int = 3, max_results: int = 50,
                               include_tests: bool = False,
                               similarity_threshold: float = 0.8) -> str:
    """
    LLM tool handler for advanced pattern search.

    Args:
        query: Search query (natural language, regex, or specific pattern)
        search_mode: Search mode ('semantic', 'fuzzy', 'regex', 'natural_language', 'hybrid')
        file_types: File extensions to search (e.g., ['.py', '.js'])
        context_lines: Number of context lines around matches
        max_results: Maximum number of results
        include_tests: Whether to include test files
        similarity_threshold: Similarity threshold for fuzzy matching

    Returns:
        Formatted search results
    """
    if not advanced_pattern_search:
        return "Advanced pattern search not available. Please check module initialization."

    try:
        # Convert string search_mode to enum
        mode_map = {
            'semantic': SearchMode.SEMANTIC,
            'fuzzy': SearchMode.FUZZY,
            'regex': SearchMode.REGEX,
            'natural_language': SearchMode.NATURAL_LANGUAGE,
            'hybrid': SearchMode.HYBRID
        }
        search_mode_enum = mode_map.get(search_mode.lower(), SearchMode.HYBRID)

        # Perform search
        results, stats = advanced_pattern_search.search(
            query=query,
            search_mode=search_mode_enum,
            file_types=file_types,
            context_lines=context_lines,
            max_results=max_results,
            include_tests=include_tests,
            similarity_threshold=similarity_threshold
        )

        # Format and return results
        formatted_results = advanced_pattern_search.format_results(results, stats)

        console.print(f"[green]✓ Advanced pattern search completed: {len(results)} matches found[/green]")
        return formatted_results

    except Exception as e:
        error_msg = f"Error in advanced pattern search: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Advanced pattern search error: {e}")
        return error_msg

def llm_smart_replace_code(search_pattern: str, replacement: str,
                          file_paths: Optional[List[str]] = None,
                          preview_only: bool = True, backup_files: bool = True,
                          git_commit: bool = False,
                          replacement_mode: str = "simple") -> str:
    """
    LLM tool handler for smart code replacement.

    Args:
        search_pattern: Pattern to search for
        replacement: Replacement text or code
        file_paths: Specific files to modify (optional)
        preview_only: Only show preview without making changes
        backup_files: Create backup files before modification
        git_commit: Automatically commit changes to Git
        replacement_mode: Type of replacement ('simple', 'regex', 'function_signature', 'variable_rename')

    Returns:
        Formatted replacement results and preview
    """
    if not smart_code_replacer:
        return "Smart code replacer not available. Please check module initialization."

    try:
        # Convert string replacement_mode to enum
        mode_map = {
            'simple': ReplacementMode.SIMPLE,
            'regex': ReplacementMode.REGEX,
            'function_signature': ReplacementMode.FUNCTION_SIGNATURE,
            'variable_rename': ReplacementMode.VARIABLE_RENAME,
            'class_rename': ReplacementMode.CLASS_RENAME,
            'import_update': ReplacementMode.IMPORT_UPDATE
        }
        replacement_mode_enum = mode_map.get(replacement_mode.lower(), ReplacementMode.SIMPLE)

        # Perform replacement
        results, previews = smart_code_replacer.replace_code(
            search_pattern=search_pattern,
            replacement=replacement,
            file_paths=file_paths,
            preview_only=preview_only,
            backup_files=backup_files,
            git_commit=git_commit,
            replacement_mode=replacement_mode_enum
        )

        # Format results
        output = []

        if preview_only:
            output.append("🔍 REPLACEMENT PREVIEW:")
            output.append("=" * 50)
            preview_text = smart_code_replacer.format_preview(previews)
            output.append(preview_text)

            # Summary
            total_changes = sum(r.changes_made for r in results if r.success)
            affected_files = len([r for r in results if r.success and r.changes_made > 0])
            output.append(f"\n📊 Summary: {total_changes} potential changes in {affected_files} files")
            output.append("\n💡 To apply changes, set preview_only=false")
        else:
            output.append("✅ REPLACEMENT COMPLETED:")
            output.append("=" * 50)

            successful_results = [r for r in results if r.success]
            failed_results = [r for r in results if not r.success]

            if successful_results:
                total_changes = sum(r.changes_made for r in successful_results)
                output.append(f"✓ Successfully modified {len(successful_results)} files with {total_changes} changes")

                for result in successful_results[:5]:  # Show first 5 files
                    output.append(f"  • {result.file_path}: {result.changes_made} changes")
                    if result.backup_path:
                        output.append(f"    Backup: {result.backup_path}")

                if len(successful_results) > 5:
                    output.append(f"  ... and {len(successful_results) - 5} more files")

            if failed_results:
                output.append(f"\n⚠ Failed to modify {len(failed_results)} files:")
                for result in failed_results:
                    output.append(f"  • {result.file_path}: {result.error_message}")

        formatted_output = "\n".join(output)

        if not preview_only:
            console.print(f"[green]✓ Smart code replacement completed[/green]")
        else:
            console.print(f"[blue]ℹ Smart code replacement preview generated[/blue]")

        return formatted_output

    except Exception as e:
        error_msg = f"Error in smart code replacement: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Smart code replacement error: {e}")
        return error_msg

def llm_smart_file_search(query: str, search_type: str = "text",
                         file_patterns: Optional[List[str]] = None,
                         max_results: int = 100, include_context: bool = True,
                         context_lines: int = 3, case_sensitive: bool = False,
                         whole_words: bool = False, include_hidden: bool = False) -> str:
    """
    LLM tool handler for smart file search.

    Args:
        query: Search query
        search_type: Type of search ('text', 'regex', 'function', 'class', 'variable', 'import', 'comment', 'semantic', 'fuzzy')
        file_patterns: File patterns to include
        max_results: Maximum number of results
        include_context: Whether to include code context
        context_lines: Number of context lines
        case_sensitive: Whether search is case sensitive
        whole_words: Whether to match whole words only
        include_hidden: Whether to include hidden files

    Returns:
        Formatted search results
    """
    if not smart_file_search:
        return "Smart file search not available. Please check module initialization."

    try:
        # Convert string search_type to enum
        type_map = {
            'text': SearchType.TEXT,
            'regex': SearchType.REGEX,
            'function': SearchType.FUNCTION,
            'class': SearchType.CLASS,
            'variable': SearchType.VARIABLE,
            'import': SearchType.IMPORT,
            'comment': SearchType.COMMENT,
            'semantic': SearchType.SEMANTIC,
            'fuzzy': SearchType.FUZZY
        }
        search_type_enum = type_map.get(search_type.lower(), SearchType.TEXT)

        # Perform search
        results, stats = smart_file_search.search(
            query=query,
            search_type=search_type_enum,
            file_patterns=file_patterns,
            max_results=max_results,
            include_context=include_context,
            context_lines=context_lines,
            case_sensitive=case_sensitive,
            whole_words=whole_words,
            include_hidden=include_hidden
        )

        # Format and return results
        formatted_results = smart_file_search.format_results(results, stats, show_context=include_context)

        console.print(f"[green]✓ Smart file search completed: {len(results)} matches found[/green]")
        return formatted_results

    except Exception as e:
        error_msg = f"Error in smart file search: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Smart file search error: {e}")
        return error_msg

def llm_create_execution_plan(user_request: str, strategy: str = "waterfall",
                             auto_execute: bool = False, auto_commit: bool = False,
                             context: Optional[Dict[str, Any]] = None) -> str:
    """
    LLM tool handler for creating execution plans.

    Args:
        user_request: Natural language description of what to accomplish
        strategy: Planning strategy ('waterfall', 'agile', 'incremental', 'parallel')
        auto_execute: Whether to automatically execute the plan
        auto_commit: Whether to automatically commit changes to Git
        context: Additional context information

    Returns:
        Formatted plan creation results
    """
    if not auto_planner:
        return "Auto planner not available. Please check module initialization."

    try:
        # Convert string strategy to enum
        strategy_map = {
            'waterfall': PlanningStrategy.WATERFALL,
            'agile': PlanningStrategy.AGILE,
            'incremental': PlanningStrategy.INCREMENTAL,
            'parallel': PlanningStrategy.PARALLEL
        }
        strategy_enum = strategy_map.get(strategy.lower(), PlanningStrategy.WATERFALL)

        # Create the plan
        planning_result = auto_planner.create_plan(
            user_request=user_request,
            strategy=strategy_enum,
            context=context
        )

        if not planning_result.success:
            return f"Failed to create plan: {planning_result.message}"

        plan = planning_result.plan

        # Format plan information
        output = []
        output.append(f"✅ EXECUTION PLAN CREATED")
        output.append("=" * 50)
        output.append(f"Plan ID: {plan.id}")
        output.append(f"Name: {plan.name}")
        output.append(f"Strategy: {plan.strategy.value}")
        output.append(f"Estimated Duration: {plan.total_estimated_duration:.1f} minutes")
        output.append(f"Total Tasks: {len(plan.tasks)}")
        output.append("")

        # List tasks
        output.append("📋 TASKS:")
        for i, task in enumerate(plan.tasks, 1):
            deps = f" (depends on: {', '.join(task.dependencies)})" if task.dependencies else ""
            output.append(f"{i:2d}. {task.name} [{task.task_type.value}] - {task.estimated_duration:.1f}min{deps}")

        # Show recommendations
        if planning_result.recommendations:
            output.append("\n💡 RECOMMENDATIONS:")
            for rec in planning_result.recommendations:
                output.append(f"   • {rec}")

        # Show warnings
        if planning_result.warnings:
            output.append("\n⚠️ WARNINGS:")
            for warning in planning_result.warnings:
                output.append(f"   • {warning}")

        # Auto-execute if requested
        if auto_execute:
            output.append("\n🚀 AUTO-EXECUTING PLAN...")
            execution_result = auto_planner.execute_plan(plan, auto_commit=auto_commit)

            output.append(f"\n📊 EXECUTION RESULTS:")
            output.append(f"   Success: {execution_result['success']}")
            output.append(f"   Completed Tasks: {execution_result['completed_tasks']}")
            output.append(f"   Failed Tasks: {execution_result['failed_tasks']}")
            output.append(f"   Duration: {execution_result['total_duration']:.1f}s")

            if execution_result['error_messages']:
                output.append(f"\n❌ ERRORS:")
                for error in execution_result['error_messages']:
                    output.append(f"   • {error}")
        else:
            output.append(f"\n💡 To execute this plan, use: execute_plan(plan_id='{plan.id}')")

        formatted_output = "\n".join(output)
        console.print(f"[green]✓ Execution plan created successfully[/green]")
        return formatted_output

    except Exception as e:
        error_msg = f"Error creating execution plan: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Auto planner error: {e}")
        return error_msg

def llm_execute_plan(plan_id: str, auto_commit: bool = False) -> str:
    """
    LLM tool handler for executing plans.

    Args:
        plan_id: ID of the plan to execute
        auto_commit: Whether to automatically commit changes to Git

    Returns:
        Formatted execution results
    """
    if not auto_planner:
        return "Auto planner not available. Please check module initialization."

    try:
        # Load the plan
        plan = auto_planner.load_plan(plan_id)
        if not plan:
            return f"Plan not found: {plan_id}"

        # Execute the plan
        execution_result = auto_planner.execute_plan(plan, auto_commit=auto_commit)

        # Format results
        output = []
        output.append(f"🚀 PLAN EXECUTION COMPLETED")
        output.append("=" * 50)
        output.append(f"Plan: {plan.name}")
        output.append(f"Strategy: {plan.strategy.value}")
        output.append(f"Success: {execution_result['success']}")
        output.append(f"Duration: {execution_result['total_duration']:.1f}s")
        output.append("")

        output.append(f"📊 TASK SUMMARY:")
        output.append(f"   ✅ Completed: {execution_result['completed_tasks']}")
        output.append(f"   ❌ Failed: {execution_result['failed_tasks']}")
        output.append(f"   ⏭️ Skipped: {execution_result['skipped_tasks']}")
        output.append(f"   📈 Success Rate: {plan.success_rate:.1%}")

        # Show task details
        if execution_result['task_results']:
            output.append("\n📋 TASK DETAILS:")
            for task_result in execution_result['task_results']:
                status_icon = "✅" if task_result['status'] == 'completed' else "❌" if task_result['status'] == 'failed' else "⏭️"
                output.append(f"   {status_icon} {task_result['task_name']} ({task_result['duration']:.1f}s)")

        # Show errors if any
        if execution_result['error_messages']:
            output.append(f"\n❌ ERRORS:")
            for error in execution_result['error_messages']:
                output.append(f"   • {error}")

        formatted_output = "\n".join(output)
        console.print(f"[green]✓ Plan execution completed[/green]")
        return formatted_output

    except Exception as e:
        error_msg = f"Error executing plan: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Plan execution error: {e}")
        return error_msg

def llm_list_execution_plans() -> str:
    """
    LLM tool handler for listing execution plans.

    Returns:
        Formatted list of execution plans
    """
    if not auto_planner:
        return "Auto planner not available. Please check module initialization."

    try:
        plans = auto_planner.list_plans()

        if not plans:
            return "No execution plans found."

        output = []
        output.append(f"📋 EXECUTION PLANS ({len(plans)} total)")
        output.append("=" * 60)

        for plan in plans:
            created_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(plan['created_at']))
            status_icon = "✅" if plan['status'] == 'completed' else "❌" if plan['status'] == 'failed' else "🔄" if plan['status'] == 'in_progress' else "⏸️"

            output.append(f"{status_icon} {plan['name']}")
            output.append(f"   ID: {plan['id']}")
            output.append(f"   Status: {plan['status']}")
            output.append(f"   Tasks: {plan['task_count']}")
            output.append(f"   Success Rate: {plan['success_rate']:.1%}")
            output.append(f"   Created: {created_time}")
            output.append("")

        formatted_output = "\n".join(output)
        console.print(f"[green]✓ Listed {len(plans)} execution plans[/green]")
        return formatted_output

    except Exception as e:
        error_msg = f"Error listing plans: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Plan listing error: {e}")
        return error_msg

def llm_fix_code_input(code: str, language: Optional[str] = None,
                      fix_types: Optional[List[str]] = None) -> str:
    """
    LLM tool handler for fixing code input.

    Args:
        code: The code to fix
        language: Programming language (auto-detected if None)
        fix_types: Specific types of fixes to apply

    Returns:
        Formatted fix results
    """
    if not input_fixer:
        return "Input fixer not available. Please check module initialization."

    try:
        # Convert string language to enum
        language_enum = None
        if language:
            language_map = {
                'python': Language.PYTHON,
                'javascript': Language.JAVASCRIPT,
                'typescript': Language.TYPESCRIPT,
                'java': Language.JAVA,
                'cpp': Language.CPP,
                'c': Language.C,
                'json': Language.JSON,
                'html': Language.HTML,
                'css': Language.CSS,
                'sql': Language.SQL,
                'bash': Language.BASH
            }
            language_enum = language_map.get(language.lower())

        # Convert string fix types to enums
        fix_types_enum = None
        if fix_types:
            fix_type_map = {
                'syntax_error': FixType.SYNTAX_ERROR,
                'indentation': FixType.INDENTATION,
                'formatting': FixType.FORMATTING,
                'imports': FixType.IMPORTS,
                'brackets': FixType.BRACKETS,
                'quotes': FixType.QUOTES,
                'encoding': FixType.ENCODING,
                'whitespace': FixType.WHITESPACE,
                'semicolons': FixType.SEMICOLONS,
                'trailing_commas': FixType.TRAILING_COMMAS
            }
            fix_types_enum = [fix_type_map.get(ft) for ft in fix_types if ft in fix_type_map]
            fix_types_enum = [ft for ft in fix_types_enum if ft is not None]

        # Fix the code
        result = input_fixer.fix_code(
            code=code,
            language=language_enum,
            fix_types=fix_types_enum
        )

        # Format and return results
        formatted_result = input_fixer.format_result(result)

        if result.success:
            console.print(f"[green]✓ Code fixing completed successfully[/green]")
        else:
            console.print(f"[red]✗ Code fixing failed[/red]")

        return formatted_result

    except Exception as e:
        error_msg = f"Error fixing code input: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Input fixer error: {e}")
        return error_msg

def llm_build_file_index(force_rebuild: bool = False) -> str:
    """
    LLM tool handler for building file index.

    Args:
        force_rebuild: Whether to rebuild the entire index

    Returns:
        Formatted index building results
    """
    if not file_indexer:
        return "File indexer not available. Please check module initialization."

    try:
        console.print(f"[blue]🔍 Building file index (force_rebuild={force_rebuild})...[/blue]")

        # Build index with progress callback
        def progress_callback(message):
            console.print(f"[blue]   {message}[/blue]")

        stats = file_indexer.build_index(
            force_rebuild=force_rebuild,
            progress_callback=progress_callback
        )

        # Format results
        output = []
        output.append("✅ FILE INDEX BUILD COMPLETED")
        output.append("=" * 50)
        output.append(f"Files Processed: {stats['files_processed']}")
        output.append(f"Files Updated: {stats['files_updated']}")
        output.append(f"Files Skipped: {stats['files_skipped']}")
        output.append(f"Errors: {stats['errors']}")
        output.append(f"Total Time: {stats['total_time']:.2f}s")

        # Get index statistics
        index_stats = file_indexer.get_index_stats()
        output.append(f"\n📊 INDEX STATISTICS:")
        output.append(f"   Total Files: {index_stats['total_files']}")
        output.append(f"   Total Size: {index_stats['total_size'] / (1024*1024):.1f} MB")
        output.append(f"   Total Lines: {index_stats['total_lines']:,}")

        if index_stats['file_types']:
            output.append(f"\n📁 FILE TYPES:")
            for file_type, count in sorted(index_stats['file_types'].items(), key=lambda x: x[1], reverse=True):
                output.append(f"   {file_type}: {count}")

        if index_stats['languages']:
            output.append(f"\n💻 LANGUAGES:")
            for language, count in sorted(index_stats['languages'].items(), key=lambda x: x[1], reverse=True):
                output.append(f"   {language}: {count}")

        if index_stats['semantic_index_available']:
            output.append(f"\n🧠 Semantic search enabled")

        formatted_output = "\n".join(output)
        console.print(f"[green]✓ File index build completed successfully[/green]")
        return formatted_output

    except Exception as e:
        error_msg = f"Error building file index: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"File indexer error: {e}")
        return error_msg

def llm_search_file_index(query: str, search_type: str = "full_text",
                         max_results: int = 50,
                         file_types: Optional[List[str]] = None) -> str:
    """
    LLM tool handler for searching file index.

    Args:
        query: Search query
        search_type: Type of search ('full_text', 'semantic', 'structural', 'metadata')
        max_results: Maximum number of results
        file_types: Filter by file types

    Returns:
        Formatted search results
    """
    if not file_indexer:
        return "File indexer not available. Please check module initialization."

    try:
        # Convert string search_type to enum
        type_map = {
            'full_text': IndexType.FULL_TEXT,
            'semantic': IndexType.SEMANTIC,
            'structural': IndexType.STRUCTURAL,
            'metadata': IndexType.METADATA
        }
        search_type_enum = type_map.get(search_type.lower(), IndexType.FULL_TEXT)

        # Convert string file_types to enums
        file_types_enum = None
        if file_types:
            file_type_map = {
                'source_code': FileType.SOURCE_CODE,
                'documentation': FileType.DOCUMENTATION,
                'configuration': FileType.CONFIGURATION,
                'data': FileType.DATA,
                'binary': FileType.BINARY,
                'unknown': FileType.UNKNOWN
            }
            file_types_enum = [file_type_map.get(ft) for ft in file_types if ft in file_type_map]
            file_types_enum = [ft for ft in file_types_enum if ft is not None]

        # Perform search
        results = file_indexer.search(
            query=query,
            search_type=search_type_enum,
            max_results=max_results,
            file_types=file_types_enum
        )

        # Format and return results
        formatted_results = file_indexer.format_search_results(results)

        console.print(f"[green]✓ File index search completed: {len(results)} results found[/green]")
        return formatted_results

    except Exception as e:
        error_msg = f"Error searching file index: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"File index search error: {e}")
        return error_msg

def llm_get_index_stats() -> str:
    """
    LLM tool handler for getting index statistics.

    Returns:
        Formatted index statistics
    """
    if not file_indexer:
        return "File indexer not available. Please check module initialization."

    try:
        stats = file_indexer.get_index_stats()

        output = []
        output.append("📊 FILE INDEX STATISTICS")
        output.append("=" * 50)

        if stats['total_files'] == 0:
            output.append("No files indexed. Run build_file_index() to create the index.")
            return "\n".join(output)

        output.append(f"Total Files: {stats['total_files']:,}")
        output.append(f"Total Size: {stats['total_size'] / (1024*1024):.1f} MB")
        output.append(f"Total Lines: {stats['total_lines']:,}")

        if stats['last_updated']:
            import time
            last_updated = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(stats['last_updated']))
            output.append(f"Last Updated: {last_updated}")

        output.append(f"Semantic Index: {'✅ Available' if stats['semantic_index_available'] else '❌ Not Available'}")

        if stats['file_types']:
            output.append(f"\n📁 FILE TYPES:")
            for file_type, count in sorted(stats['file_types'].items(), key=lambda x: x[1], reverse=True):
                percentage = (count / stats['total_files']) * 100
                output.append(f"   {file_type}: {count} ({percentage:.1f}%)")

        if stats['languages']:
            output.append(f"\n💻 PROGRAMMING LANGUAGES:")
            for language, count in sorted(stats['languages'].items(), key=lambda x: x[1], reverse=True):
                percentage = (count / stats['total_files']) * 100
                output.append(f"   {language}: {count} ({percentage:.1f}%)")

        formatted_output = "\n".join(output)
        console.print(f"[green]✓ Index statistics retrieved[/green]")
        return formatted_output

    except Exception as e:
        error_msg = f"Error getting index statistics: {str(e)}"
        console.print(f"[red]✗ {error_msg}[/red]")
        logger.error(f"Index stats error: {e}")
        return error_msg

def llm_show_advanced_tools_help(tool_name: Optional[str] = "all") -> str:
    """
    LLM tool handler for showing advanced tools help and examples.

    Args:
        tool_name: Specific tool to show help for, or 'all' for complete guide

    Returns:
        Comprehensive help and examples for the requested tools
    """

    help_content = {
        "advanced_pattern_search": """
🔍 ADVANCED PATTERN SEARCH - Comprehensive Help

PURPOSE: Semantic understanding and complex pattern matching across entire codebase

FUNCTION: advanced_pattern_search()

KEY PARAMETERS:
• query: Search query (natural language, regex, or specific pattern)
• search_mode: "semantic", "fuzzy", "regex", "natural_language", "hybrid" (recommended)
• file_types: File extensions to search (e.g., ['.py', '.js'])
• max_results: Maximum number of results (default: 50)
• context_lines: Number of context lines around matches (default: 3)
• similarity_threshold: Similarity threshold for fuzzy matching (default: 0.8)

PRACTICAL EXAMPLES:

1. Find all authentication-related code:
   advanced_pattern_search(
       query="authentication jwt token validation middleware",
       search_mode="semantic",
       file_types=[".py", ".js"],
       max_results=20
   )

2. Complex regex pattern search:
   advanced_pattern_search(
       query="gemini.*model|GEMINI.*MODEL|model.*gemini",
       search_mode="regex",
       context_lines=5
   )

3. Natural language search:
   advanced_pattern_search(
       query="find all database connection pooling implementations",
       search_mode="natural_language"
   )

4. Fuzzy search (typo-tolerant):
   advanced_pattern_search(
       query="autentication",  # Misspelled
       search_mode="fuzzy",
       similarity_threshold=0.8
   )

5. Hybrid search (BEST - combines all modes):
   advanced_pattern_search(
       query="api rate limiting implementation",
       search_mode="hybrid",
       max_results=25
   )

WHEN TO USE:
• Exploring unfamiliar codebases
• Finding complex patterns across multiple files
• Understanding code architecture and relationships
• Locating specific functionality with semantic understanding
""",

        "smart_replace_code": """
🔧 SMART CODE REPLACEMENT - Comprehensive Help

PURPOSE: Intelligent string and code replacement with preview, Git integration, and complex refactoring

FUNCTION: smart_replace_code()

KEY PARAMETERS:
• search_pattern: Pattern to search for (supports regex)
• replacement: Replacement text or code
• replacement_mode: "simple", "regex", "function_signature", "variable_rename"
• preview_only: Show preview without making changes (default: true)
• backup_files: Create backup files before modification (default: true)
• git_commit: Automatically commit changes to Git (default: false)
• file_paths: Specific files to modify (optional)

PRACTICAL EXAMPLES:

1. Preview function name changes:
   smart_replace_code(
       search_pattern="old_function_name",
       replacement="new_function_name",
       replacement_mode="function_signature",
       preview_only=True
   )

2. Apply changes with Git commit:
   smart_replace_code(
       search_pattern="deprecated_api",
       replacement="new_api",
       preview_only=False,
       git_commit=True,
       backup_files=True
   )

3. Regex-based replacement:
   smart_replace_code(
       search_pattern=r"print\\s*\\(([^)]+)\\)",
       replacement=r"logger.info(\\1)",
       replacement_mode="regex",
       file_paths=["src/"]
   )

4. Variable renaming with scope awareness:
   smart_replace_code(
       search_pattern="db_connection",
       replacement="database_pool",
       replacement_mode="variable_rename"
   )

5. Class renaming:
   smart_replace_code(
       search_pattern="UserManager",
       replacement="UserService",
       replacement_mode="class_rename",
       preview_only=True
   )

SAFETY FEATURES:
• Always preview first with preview_only=True
• Automatic backup creation
• Git integration for change tracking
• Scope-aware replacements to avoid conflicts

WHEN TO USE:
• Refactoring code across multiple files
• Renaming functions, classes, or variables
• Updating API calls or deprecated methods
• Systematic code improvements
""",

        "smart_file_search": """
🔎 SMART FILE SEARCH (grep++) - Comprehensive Help

PURPOSE: Advanced grep-like content search with code context understanding and semantic ranking

FUNCTION: smart_file_search()

KEY PARAMETERS:
• query: Search query
• search_type: "text", "regex", "function", "class", "variable", "import", "comment", "semantic", "fuzzy"
• file_patterns: File patterns to include (e.g., ['*.py', '*.js'])
• max_results: Maximum number of results (default: 100)
• include_context: Whether to include code context (default: true)
• context_lines: Number of context lines (default: 3)
• case_sensitive: Whether search is case sensitive (default: false)

PRACTICAL EXAMPLES:

1. Find function definitions:
   smart_file_search(
       query="authenticate",
       search_type="function",
       include_context=True,
       context_lines=5
   )

2. Search for classes:
   smart_file_search(
       query="Model",
       search_type="class",
       file_patterns=["*.py", "*.js"]
   )

3. Find import statements:
   smart_file_search(
       query="flask",
       search_type="import",
       include_context=True
   )

4. Semantic search:
   smart_file_search(
       query="database connection error handling",
       search_type="semantic",
       max_results=15
   )

5. Search comments and TODOs:
   smart_file_search(
       query="TODO",
       search_type="comment",
       file_patterns=["*.py", "*.js"]
   )

6. Fuzzy search (typo-tolerant):
   smart_file_search(
       query="autentication",  # Misspelled
       search_type="fuzzy"
   )

ADVANCED FEATURES:
• Context-aware results with surrounding code
• Relevance scoring and intelligent ranking
• Multi-language syntax understanding
• File type filtering and pattern matching

WHEN TO USE:
• Quick content search across files
• Finding specific code elements (functions, classes)
• Exploring code structure and organization
• Locating TODOs, comments, or documentation
""",

        "auto_planner": """
🤖 AUTO PLANNER - Comprehensive Help

PURPOSE: Autonomous task decomposition and execution with progress tracking and Git integration

FUNCTIONS: create_execution_plan(), execute_plan(), list_execution_plans()

KEY PARAMETERS for create_execution_plan():
• user_request: Natural language description of what to accomplish
• strategy: "waterfall", "agile", "incremental", "parallel"
• auto_execute: Whether to automatically execute the plan (default: false)
• auto_commit: Whether to automatically commit changes to Git (default: false)
• context: Additional context information for planning

PRACTICAL EXAMPLES:

1. Create a comprehensive development plan:
   create_execution_plan(
       user_request="Add user authentication with JWT tokens and rate limiting",
       strategy="waterfall",
       auto_execute=False,
       auto_commit=True
   )

2. Parallel execution plan:
   create_execution_plan(
       user_request="Refactor codebase for better performance",
       strategy="parallel",
       auto_execute=True
   )

3. Execute an existing plan:
   execute_plan(
       plan_id="plan_1234567890",
       auto_commit=True
   )

4. List all execution plans:
   list_execution_plans()

5. Agile development plan:
   create_execution_plan(
       user_request="Implement user dashboard with real-time updates",
       strategy="agile",
       context={"sprint_length": "2_weeks"}
   )

PLANNING STRATEGIES:
• Waterfall: Sequential execution with dependencies
• Agile: Iterative development with feedback loops
• Incremental: Step-by-step improvements
• Parallel: Concurrent task execution

FEATURES:
• Intelligent task breakdown
• Dependency management
• Progress tracking
• Error handling and recovery
• Git integration for change tracking

WHEN TO USE:
• Complex development tasks
• Systematic code changes
• Project planning and execution
• Coordinating multiple related changes
""",

        "input_fixer": """
🔧 INPUT FIXER - Comprehensive Help

PURPOSE: Automatic code fixing for malformed input, syntax errors, and formatting issues across multiple languages

FUNCTION: fix_code_input()

KEY PARAMETERS:
• code: The code to fix
• language: Programming language (auto-detected if not specified)
  - Supported: "python", "javascript", "java", "cpp", "json", "html", "css", "sql", "bash"
• fix_types: Specific types of fixes to apply (optional)
  - Available: "syntax_error", "indentation", "formatting", "imports", "brackets", "quotes", "encoding", "whitespace"

PRACTICAL EXAMPLES:

1. Fix Python syntax errors:
   fix_code_input(
       code='''
       def broken_function(
           print "Hello World"
           if x = 5
               return True
       ''',
       language="python"
   )

2. Fix JavaScript issues:
   fix_code_input(
       code='''
       function greetUser(name {
           console.log("Hello " + name)
           return true
       }
       ''',
       language="javascript"
   )

3. Fix JSON formatting:
   fix_code_input(
       code='{"name": "John", "age": 30,}',
       language="json"
   )

4. Auto-detect language and fix all issues:
   fix_code_input(malformed_code_string)

5. Fix specific issues only:
   fix_code_input(
       code=problematic_code,
       fix_types=["brackets", "indentation", "formatting"]
   )

SUPPORTED LANGUAGES:
• Python: Syntax, indentation, imports, formatting
• JavaScript: Brackets, semicolons, formatting
• Java: Syntax, imports, formatting
• JSON: Structure, quotes, formatting
• HTML/CSS: Tags, attributes, formatting
• SQL: Syntax, formatting
• Bash: Syntax, quoting

FEATURES:
• Automatic language detection
• Intelligent error analysis
• Multiple fix types
• Confidence scoring
• Before/after comparison

WHEN TO USE:
• Cleaning up malformed code
• Fixing syntax errors
• Standardizing code formatting
• Processing code from various sources
""",

        "file_indexer": """
📚 FILE INDEXING & CODEBASE CONTEXT - Comprehensive Help

PURPOSE: Efficient indexing system for large files and codebases with semantic understanding and real-time updates

FUNCTIONS: build_file_index(), search_file_index(), get_index_stats()

KEY PARAMETERS for build_file_index():
• force_rebuild: Whether to rebuild the entire index (default: false)

KEY PARAMETERS for search_file_index():
• query: Search query
• search_type: "full_text", "semantic", "structural", "metadata"
• max_results: Maximum number of results (default: 50)
• file_types: Filter by file types ("source_code", "documentation", "configuration", etc.)

PRACTICAL EXAMPLES:

1. Build comprehensive index:
   build_file_index(force_rebuild=False)

2. Semantic search in index:
   search_file_index(
       query="authentication middleware implementation",
       search_type="semantic",
       max_results=25,
       file_types=["source_code"]
   )

3. Structural search:
   search_file_index(
       query="UserManager",
       search_type="structural",
       max_results=20
   )

4. Full-text search:
   search_file_index(
       query="database connection pool",
       search_type="full_text",
       file_types=["source_code", "documentation"]
   )

5. Get index statistics:
   get_index_stats()

INDEX TYPES:
• Full-text: Traditional text-based search
• Semantic: TF-IDF vectorization for intelligent search
• Structural: Functions, classes, imports, variables
• Metadata: File information and properties

FEATURES:
• Real-time file system monitoring
• Incremental updates
• Multi-threaded indexing
• Semantic understanding
• Structural analysis
• Performance optimization

WHEN TO USE:
• Large codebases (1000+ files)
• Semantic code understanding
• Fast navigation and search
• Code exploration and analysis
• Performance-critical search operations
"""
    }

    if tool_name == "all":
        output = []
        output.append("🚀 CODY ADVANCED AI CODING TOOLS - COMPLETE GUIDE")
        output.append("=" * 70)
        output.append("All tools are integrated and ready to use!")
        output.append("")

        for tool, content in help_content.items():
            output.append(content)
            output.append("\n" + "=" * 70 + "\n")

        output.append("💡 BEST PRACTICES:")
        output.append("1. Start with build_file_index() for better search results")
        output.append("2. Use preview_only=True before applying changes")
        output.append("3. Enable backups with backup_files=True")
        output.append("4. Use semantic search for intelligent results")
        output.append("5. Combine tools for complex workflows")
        output.append("6. Create execution plans for multi-step tasks")
        output.append("7. Use hybrid search mode for best results")
        output.append("8. Enable Git integration for change tracking")
        output.append("")
        output.append("🎯 For specific examples, see: examples/advanced_features_examples.py")
        output.append("📚 For detailed docs, see: docs/ADVANCED_FEATURES.md")

    elif tool_name in help_content:
        output = [help_content[tool_name]]
    else:
        output = [f"❌ Unknown tool: {tool_name}. Available tools: {', '.join(help_content.keys())}, 'all'"]

    formatted_output = "\n".join(output)
    console.print(f"[green]✓ Advanced tools help displayed[/green]")
    return formatted_output

# -----------------------------------------------------------------------------
# 9.5. ADVANCED TOOL IMPLEMENTATIONS (NEW CAPABILITIES)
# -----------------------------------------------------------------------------

def llm_intelligent_code_completion(file_path: str, cursor_position: Dict[str, int], context_lines: int = 10, completion_type: str = "auto") -> str:
    """Provide intelligent code completion and suggestions."""
    try:
        if not Path(file_path).exists():
            return f"❌ File not found: {file_path}"

        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        line_num = cursor_position.get('line', 1) - 1  # Convert to 0-based
        col_num = cursor_position.get('column', 0)

        # Get context around cursor
        start_line = max(0, line_num - context_lines)
        end_line = min(len(lines), line_num + context_lines + 1)

        # Analyze current line and position
        current_line = lines[line_num] if line_num < len(lines) else ""
        prefix = current_line[:col_num]

        # Determine file type
        file_ext = Path(file_path).suffix.lower()
        language_map = {'.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
                       '.java': 'Java', '.cpp': 'C++', '.c': 'C', '.go': 'Go'}
        language = language_map.get(file_ext, 'Unknown')

        # Generate completions based on context
        completions = []

        if completion_type in ["function", "auto"] and ("def " in prefix or "function " in prefix):
            completions.extend([
                "def function_name(self, param1, param2):",
                "async def async_function(self):",
                "def __init__(self):",
                "def __str__(self):",
                "def __repr__(self):"
            ])

        if completion_type in ["import", "auto"] and ("import " in prefix or "from " in prefix):
            common_imports = {
                '.py': ["import os", "import sys", "import json", "from typing import List, Dict", "import asyncio"],
                '.js': ["import React from 'react'", "import { useState } from 'react'", "const fs = require('fs')"],
                '.ts': ["import { Component } from '@angular/core'", "import * as fs from 'fs'"]
            }
            completions.extend(common_imports.get(file_ext, []))

        if completion_type in ["class", "auto"] and "class " in prefix:
            completions.extend([
                "class ClassName:",
                "class ClassName(BaseClass):",
                "class ClassName(Exception):"
            ])

        # Add context-aware suggestions
        result = f"🧠 **Intelligent Code Completion for {language}**\n\n"
        result += f"📍 **Position:** Line {line_num + 1}, Column {col_num + 1}\n"
        result += f"📝 **Current Line:** `{current_line.strip()}`\n\n"

        if completions:
            result += "💡 **Suggestions:**\n"
            for i, completion in enumerate(completions[:5], 1):
                result += f"{i}. `{completion}`\n"
        else:
            result += "💭 **Context Analysis:** Continue typing for more specific suggestions.\n"

        return result

    except Exception as e:
        return f"❌ Error in code completion: {str(e)}"

def llm_advanced_code_analysis(file_paths: List[str], analysis_types: List[str] = None, severity_threshold: str = "medium", include_suggestions: bool = True) -> str:
    """Perform comprehensive code analysis."""
    try:
        if not file_paths:
            return "❌ No files provided for analysis"

        analysis_types = analysis_types or ["complexity", "security", "performance", "maintainability"]
        results = []

        for file_path in file_paths:
            if not Path(file_path).exists():
                results.append(f"❌ File not found: {file_path}")
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_analysis = f"\n📁 **Analysis for {Path(file_path).name}**\n"

                # Basic metrics
                lines = content.split('\n')
                file_analysis += f"📊 **Basic Metrics:**\n"
                file_analysis += f"• Lines of code: {len(lines)}\n"
                file_analysis += f"• File size: {len(content)} bytes\n"

                # Complexity analysis
                if "complexity" in analysis_types:
                    complexity_score = analyze_complexity(content)
                    file_analysis += f"• Complexity score: {complexity_score}/10\n"

                # Security analysis
                if "security" in analysis_types:
                    security_issues = analyze_security(content, file_path)
                    if security_issues:
                        file_analysis += f"\n🔒 **Security Issues:**\n"
                        for issue in security_issues:
                            file_analysis += f"• {issue}\n"

                # Performance analysis
                if "performance" in analysis_types:
                    perf_issues = analyze_performance(content, file_path)
                    if perf_issues:
                        file_analysis += f"\n⚡ **Performance Issues:**\n"
                        for issue in perf_issues:
                            file_analysis += f"• {issue}\n"

                # Maintainability analysis
                if "maintainability" in analysis_types:
                    maint_score = analyze_maintainability(content)
                    file_analysis += f"• Maintainability score: {maint_score}/10\n"

                if include_suggestions:
                    suggestions = generate_improvement_suggestions(content, file_path)
                    if suggestions:
                        file_analysis += f"\n💡 **Improvement Suggestions:**\n"
                        for suggestion in suggestions:
                            file_analysis += f"• {suggestion}\n"

                results.append(file_analysis)

            except Exception as e:
                results.append(f"❌ Error analyzing {file_path}: {str(e)}")

        return "\n".join(results)

    except Exception as e:
        return f"❌ Error in advanced code analysis: {str(e)}"

def analyze_complexity(content: str) -> int:
    """Analyze code complexity (simplified)."""
    complexity_indicators = [
        'if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except:',
        'def ', 'class ', 'lambda ', '&&', '||', '?', 'switch', 'case'
    ]

    score = 0
    lines = content.split('\n')

    for line in lines:
        line = line.strip().lower()
        for indicator in complexity_indicators:
            if indicator in line:
                score += 1

    # Normalize to 1-10 scale
    return min(10, max(1, score // max(1, len(lines) // 10)))

def analyze_security(content: str, file_path: str) -> List[str]:
    """Analyze security issues (simplified)."""
    issues = []
    content_lower = content.lower()

    security_patterns = {
        'eval(': 'Use of eval() can lead to code injection',
        'exec(': 'Use of exec() can lead to code injection',
        'shell=true': 'Shell execution can be dangerous',
        'password': 'Potential hardcoded password',
        'secret': 'Potential hardcoded secret',
        'api_key': 'Potential hardcoded API key',
        'sql': 'Potential SQL injection risk'
    }

    for pattern, message in security_patterns.items():
        if pattern in content_lower:
            issues.append(message)

    return issues

def analyze_performance(content: str, file_path: str) -> List[str]:
    """Analyze performance issues (simplified)."""
    issues = []
    content_lower = content.lower()

    performance_patterns = {
        'sleep(': 'Blocking sleep calls can impact performance',
        'time.sleep': 'Blocking sleep calls can impact performance',
        'for.*in.*range(len(': 'Consider using enumerate() instead',
        '+ str(': 'String concatenation in loops can be slow',
        'global ': 'Global variables can impact performance'
    }

    for pattern, message in performance_patterns.items():
        if pattern in content_lower:
            issues.append(message)

    return issues

def analyze_maintainability(content: str) -> int:
    """Analyze code maintainability (simplified)."""
    lines = content.split('\n')

    # Factors that improve maintainability
    good_factors = 0
    if '"""' in content or "'''" in content:  # Docstrings
        good_factors += 2
    if '#' in content:  # Comments
        good_factors += 1
    if 'def ' in content:  # Functions
        good_factors += 1
    if 'class ' in content:  # Classes
        good_factors += 1

    # Factors that hurt maintainability
    bad_factors = 0
    avg_line_length = sum(len(line) for line in lines) / max(1, len(lines))
    if avg_line_length > 100:  # Long lines
        bad_factors += 2
    if len(lines) > 500:  # Large files
        bad_factors += 1

    score = max(1, min(10, 5 + good_factors - bad_factors))
    return score

def generate_improvement_suggestions(content: str, file_path: str) -> List[str]:
    """Generate improvement suggestions."""
    suggestions = []

    if '"""' not in content and "'''" not in content:
        suggestions.append("Add docstrings to functions and classes")

    if '#' not in content:
        suggestions.append("Add comments to explain complex logic")

    lines = content.split('\n')
    long_lines = [i for i, line in enumerate(lines) if len(line) > 100]
    if long_lines:
        suggestions.append(f"Consider breaking long lines (found {len(long_lines)} lines > 100 chars)")

    if 'TODO' in content or 'FIXME' in content:
        suggestions.append("Address TODO and FIXME comments")

    return suggestions

# -----------------------------------------------------------------------------
# 9.6. MCP TOOL INTEGRATION FUNCTIONS
# -----------------------------------------------------------------------------

def llm_mcp_search(query: str, server_name: str = "ddg-search", max_results: int = 5) -> str:
    """Search using MCP server (e.g., DuckDuckGo, Brave)."""
    try:
        if server_name not in mcp_client.servers:
            return f"❌ MCP server '{server_name}' not configured. Available servers: {list(mcp_client.servers.keys())}"

        server = mcp_client.servers[server_name]
        if server.status != "connected":
            return f"❌ MCP server '{server_name}' is not connected. Use '/mcp start {server_name}' to connect."

        # This would be implemented with actual MCP protocol calls
        # For now, return a placeholder response
        result = f"🔍 **MCP Search Results from {server_name}**\n\n"
        result += f"Query: '{query}'\n"
        result += f"Server: {server_name} (Connected)\n"
        result += f"Max Results: {max_results}\n\n"
        result += "💡 **Note**: MCP search integration is ready. Actual search results would be fetched from the connected MCP server.\n"
        result += f"Use '/mcp test {server_name}' to verify server connectivity."

        return result

    except Exception as e:
        return f"❌ Error in MCP search: {str(e)}"

def llm_mcp_file_operation(operation: str, file_path: str, content: str = None, server_name: str = "filesystem") -> str:
    """Perform file operations using MCP filesystem server."""
    try:
        if server_name not in mcp_client.servers:
            return f"❌ MCP server '{server_name}' not configured."

        server = mcp_client.servers[server_name]
        if server.status != "connected":
            return f"❌ MCP server '{server_name}' is not connected. Use '/mcp start {server_name}' to connect."

        result = f"📁 **MCP File Operation via {server_name}**\n\n"
        result += f"Operation: {operation}\n"
        result += f"File Path: {file_path}\n"

        if operation == "read":
            result += "📖 Reading file content via MCP server...\n"
        elif operation == "write":
            result += f"✏️ Writing content via MCP server...\n"
            result += f"Content Length: {len(content) if content else 0} characters\n"
        elif operation == "list":
            result += "📋 Listing directory contents via MCP server...\n"

        result += "\n💡 **Note**: MCP file operations are ready. Actual operations would be performed through the connected MCP server."

        return result

    except Exception as e:
        return f"❌ Error in MCP file operation: {str(e)}"

def llm_mcp_git_operation(operation: str, repository_path: str = ".", server_name: str = "git") -> str:
    """Perform Git operations using MCP Git server."""
    try:
        if server_name not in mcp_client.servers:
            return f"❌ MCP server '{server_name}' not configured."

        server = mcp_client.servers[server_name]
        if server.status != "connected":
            return f"❌ MCP server '{server_name}' is not connected. Use '/mcp start {server_name}' to connect."

        result = f"🔧 **MCP Git Operation via {server_name}**\n\n"
        result += f"Operation: {operation}\n"
        result += f"Repository: {repository_path}\n"

        if operation == "status":
            result += "📊 Getting Git status via MCP server...\n"
        elif operation == "log":
            result += "📜 Getting Git log via MCP server...\n"
        elif operation == "diff":
            result += "🔍 Getting Git diff via MCP server...\n"

        result += "\n💡 **Note**: MCP Git operations are ready. Actual Git commands would be executed through the connected MCP server."

        return result

    except Exception as e:
        return f"❌ Error in MCP Git operation: {str(e)}"

def llm_mcp_database_query(query: str, database_path: str, server_name: str = "sqlite") -> str:
    """Execute database queries using MCP SQLite server."""
    try:
        if server_name not in mcp_client.servers:
            return f"❌ MCP server '{server_name}' not configured."

        server = mcp_client.servers[server_name]
        if server.status != "connected":
            return f"❌ MCP server '{server_name}' is not connected. Use '/mcp start {server_name}' to connect."

        result = f"🗄️ **MCP Database Query via {server_name}**\n\n"
        result += f"Database: {database_path}\n"
        result += f"Query: {query}\n\n"
        result += "🔍 Executing query via MCP server...\n"
        result += "\n💡 **Note**: MCP database operations are ready. Actual SQL queries would be executed through the connected MCP server."

        return result

    except Exception as e:
        return f"❌ Error in MCP database query: {str(e)}"

def llm_mcp_list_servers() -> str:
    """List all configured and connected MCP servers."""
    try:
        if not mcp_client.servers:
            return "❌ No MCP servers configured. Use '/mcp config' to add servers."

        result = "🔌 **MCP Servers Status**\n\n"

        connected_count = 0
        for name, server in mcp_client.servers.items():
            status_emoji = {
                "connected": "🟢",
                "disconnected": "🔴",
                "error": "❌"
            }.get(server.status, "❓")

            result += f"{status_emoji} **{name}**\n"
            result += f"  • Command: {server.command}\n"
            result += f"  • Status: {server.status}\n"

            if server.status == "connected":
                connected_count += 1
                result += f"  • Ready for requests\n"
            elif server.last_error:
                result += f"  • Error: {server.last_error}\n"

            result += "\n"

        result += f"📊 **Summary**: {connected_count}/{len(mcp_client.servers)} servers connected\n"
        result += "\n💡 **Actions**:\n"
        result += "• Use '/mcp start' to connect all servers\n"
        result += "• Use '/mcp test <server>' to test specific servers\n"
        result += "• Use '/mcp config' to modify configuration"

        return result

    except Exception as e:
        return f"❌ Error listing MCP servers: {str(e)}"

# -----------------------------------------------------------------------------
# 10. ENHANCED TERMINAL FUNCTIONS
# -----------------------------------------------------------------------------

def normalize_command_for_platform(command: str, platform: str) -> str:
    """Normalize command for cross-platform execution."""
    command_lower = command.lower().strip()

    # Platform-specific command mappings
    command_mappings = {
        'windows': {
            'ls': 'dir /b',
            'ls -la': 'dir',
            'ls -l': 'dir',
            'cat': 'type',
            'pwd': 'cd',
            'date': 'date /t',
            'time': 'time /t',
            'clear': 'cls',
            'grep': 'findstr',
            'which': 'where',
            'ps': 'tasklist',
            'kill': 'taskkill /PID'
        },
        'linux': {
            'dir': 'ls -la',
            'cls': 'clear',
            'type': 'cat'
        },
        'darwin': {  # macOS
            'dir': 'ls -la',
            'cls': 'clear',
            'type': 'cat'
        }
    }

    # Universal commands for common requests
    universal_commands = {
        'date_time': {
            'windows': 'date /t && time /t',
            'linux': 'date',
            'darwin': 'date'
        },
        'current_directory': {
            'windows': 'cd',
            'linux': 'pwd',
            'darwin': 'pwd'
        },
        'list_files': {
            'windows': 'dir',
            'linux': 'ls -la',
            'darwin': 'ls -la'
        }
    }

    # Check for universal command patterns
    if any(word in command_lower for word in ['date', 'time']) and any(word in command_lower for word in ['show', 'get', 'current']):
        return universal_commands['date_time'].get(platform, command)
    elif any(word in command_lower for word in ['pwd', 'current directory', 'where am i']):
        return universal_commands['current_directory'].get(platform, command)
    elif any(word in command_lower for word in ['ls', 'list files', 'show files']):
        return universal_commands['list_files'].get(platform, command)

    # Apply platform-specific mappings
    if platform in command_mappings:
        mappings = command_mappings[platform]

        # Try exact match first
        if command_lower in mappings:
            return mappings[command_lower]

        # Try partial matches
        for cmd, replacement in mappings.items():
            if command_lower.startswith(cmd + ' ') or command_lower == cmd:
                return command.replace(cmd, replacement, 1)

    return command

# -----------------------------------------------------------------------------
# 11. ENHANCED WORKFLOW FUNCTIONS
# -----------------------------------------------------------------------------

async def process_enhanced_workflow(user_input: str, conversation_history: List[Dict[str, Any]]) -> None:
    """Process user input through the enhanced iterative workflow engine."""
    start_time = time.time()

    try:
        # Show workflow status
        with console.status("[bold green]🧠 Processing with Enhanced Iterative Workflow...", spinner="dots"):

            # Step 1: Natural Language Understanding (if NLP available)
            if nlp_processor:
                console.print("[dim]🔤 Natural Language Processing...[/dim]")
                nlp_result = nlp_processor.process_natural_language(user_input)
                console.print(f"[dim]   Intent: {nlp_result.intent.value} (confidence: {nlp_result.confidence:.2f})[/dim]")

            # Step 2: Enhanced Workflow Processing
            if workflow_engine:
                console.print("[dim]🔄 Executing Iterative Workflow (Execute→Analyze→Plan→Execute)...[/dim]")

                # Process through enhanced workflow engine
                workflow_result = await workflow_engine.process_user_input(user_input)

                # Show workflow progress
                if workflow_result.get('success', False):
                    operations_completed = workflow_result.get('operations_completed', 0)
                    iterations = len(workflow_result.get('iterations', []))
                    execution_time = workflow_result.get('total_execution_time', 0.0)

                    console.print(f"[dim]   ✅ Workflow completed: {operations_completed} operations in {iterations} iterations ({execution_time:.2f}s)[/dim]")

                    # Show goal achievement
                    if workflow_result.get('goal_achieved', False):
                        console.print(f"[dim]   🎯 User requirements fully satisfied![/dim]")
                    else:
                        console.print(f"[dim]   ⚠️ Partial completion - some requirements may need refinement[/dim]")
                else:
                    console.print(f"[dim]   ❌ Workflow failed: {workflow_result.get('error', 'Unknown error')}[/dim]")

            # Step 3: Codebase Integration
            if codebase_awareness and any(word in user_input.lower() for word in ['file', 'code', 'create', 'edit']):
                console.print("[dim]📁 Updating Codebase Awareness...[/dim]")
                # Update active files tracking
                changed_files = codebase_awareness.get_file_changes()
                if changed_files:
                    console.print(f"[dim]   📝 Detected changes in {len(changed_files)} files[/dim]")

        # Step 4: Generate LLM Response
        console.print("[dim]💬 Generating AI Response...[/dim]")

        # Create enhanced context for LLM
        enhanced_context = conversation_history.copy()

        # Add workflow results to context if available
        if 'workflow_result' in locals():
            workflow_summary = f"""
Enhanced Workflow Execution Summary:
- User Request: {user_input}
- Success: {workflow_result.get('success', False)}
- Operations: {workflow_result.get('operations_completed', 0)}
- Iterations: {len(workflow_result.get('iterations', []))}
- Goal Achieved: {workflow_result.get('goal_achieved', False)}
- Execution Time: {workflow_result.get('total_execution_time', 0.0):.2f}s
"""

            enhanced_context.append({
                "role": "system",
                "content": f"Enhanced iterative workflow completed. {workflow_summary}"
            })

        # Get LLM response using existing function
        try:
            managed_history = manage_conversation_history(enhanced_context)
            response = get_llm_response(managed_history)
        except Exception as e:
            console.print(f"[yellow]⚠ Error in LLM response generation: {e}[/yellow]")
            logger.warning(f"LLM response error: {e}")
            # Fallback to simple response
            response = f"I successfully processed your request through the enhanced workflow. The task has been completed."

        # Add assistant response to conversation
        conversation_history.append({"role": "assistant", "content": response})

        # Display results
        processing_time = time.time() - start_time
        console.print(f"[dim]⚡ Processed in {processing_time:.2f}s[/dim]")

        # Display main response
        console.print(f"\n[bold bright_blue]🤖 CODY:[/bold bright_blue] {response}")

        # Show smart suggestions
        analysis_results = workflow_result.get('analysis_results', [])
        if analysis_results:
            latest_analysis = analysis_results[-1]
            recommendations = latest_analysis.get('recommendations', [])
            if recommendations:
                console.print("\n[bold yellow]💡 Smart Suggestions:[/bold yellow]")
                for rec in recommendations[:3]:
                    console.print(f"   • {rec}")

        console.print()  # Empty line for spacing

    except Exception as e:
        console.print(f"[red]Enhanced workflow error: {e}[/red]")
        logger.error(f"Enhanced workflow error: {e}")

        # Enhanced fallback mechanism
        try:
            # Try to execute the command directly if it looks like a system command
            if any(cmd in user_input.lower() for cmd in ['time', 'date', 'ls', 'dir', 'pwd']):
                console.print("[dim]🔄 Attempting direct command execution...[/dim]")

                # Map common commands
                command_map = {
                    'time': 'date /t && time /t' if sys.platform.startswith('win') else 'date',
                    'date': 'date /t' if sys.platform.startswith('win') else 'date',
                    'ls': 'dir' if sys.platform.startswith('win') else 'ls',
                    'pwd': 'cd' if sys.platform.startswith('win') else 'pwd'
                }

                for keyword, command in command_map.items():
                    if keyword in user_input.lower():
                        try:
                            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
                            if result.returncode == 0:
                                response = f"Here's the result:\n```\n{result.stdout.strip()}\n```"
                            else:
                                response = f"Command executed with warnings:\n```\n{result.stderr.strip()}\n```"
                            break
                        except Exception as cmd_error:
                            response = f"I encountered an issue executing the command: {cmd_error}"
                            break
                else:
                    response = f"I encountered an issue processing your request: {user_input}. Let me try a different approach."
            else:
                response = f"I encountered an issue processing your request: {user_input}. Let me try a different approach."

        except Exception as fallback_error:
            console.print(f"[yellow]⚠ Fallback mechanism also failed: {fallback_error}[/yellow]")
            response = f"I encountered multiple issues processing your request: {user_input}. Please try rephrasing your request."

        conversation_history.append({"role": "assistant", "content": response})
        console.print(f"\n[bold bright_blue]🤖 CODY:[/bold bright_blue] {response}\n")

def generate_workflow_response(user_input: str, workflow_result: Dict[str, Any],
                             context: Any) -> str:
    """Generate intelligent response based on workflow execution results."""
    if not workflow_result['success']:
        return f"I encountered difficulties processing your request: {user_input}. {workflow_result.get('error', 'Unknown error occurred.')}"

    execution_results = workflow_result.get('execution_results', [])
    if not execution_results:
        return "I processed your request but didn't execute any specific actions."

    latest_result = execution_results[-1]
    output_data = latest_result.get('output_data', {})

    # Generate response based on task type and results
    if latest_result.get('task_type') == 'terminal_command':
        if output_data.get('success'):
            stdout = output_data.get('stdout', '').strip()
            command = output_data.get('command', '')

            if 'date' in user_input.lower() and 'time' in user_input.lower():
                return f"✅ **Current Date and Time:**\n\n📅 **{stdout}**\n\n🔧 Command executed: `{command}`\n⚡ Execution time: {output_data.get('execution_time', 0):.3f}s"
            elif 'directory' in user_input.lower() or 'pwd' in user_input.lower():
                return f"✅ **Current Directory:**\n\n📁 **{stdout}**\n\n🔧 Command executed: `{command}`\n⚡ Execution time: {output_data.get('execution_time', 0):.3f}s"
            elif 'list' in user_input.lower() or 'files' in user_input.lower():
                file_count = len(stdout.split('\n')) if stdout else 0
                return f"✅ **Directory Contents ({file_count} items):**\n\n```\n{stdout}\n```\n\n🔧 Command executed: `{command}`\n⚡ Execution time: {output_data.get('execution_time', 0):.3f}s"
            else:
                return f"✅ **Command executed successfully:**\n\n```\n{stdout}\n```\n\n🔧 Command: `{command}`\n⚡ Execution time: {output_data.get('execution_time', 0):.3f}s"
        else:
            error = output_data.get('stderr', 'Unknown error')
            return f"Command execution failed: {error}"

    elif latest_result.get('task_type') == 'file_operation':
        operation = output_data.get('operation', '')
        file_path = output_data.get('source_path', '')

        if operation == 'create':
            return f"Successfully created file: `{file_path}`"
        elif operation == 'read':
            content_length = len(output_data.get('content', ''))
            return f"Successfully read file `{file_path}` ({content_length} characters)"
        else:
            return f"File operation '{operation}' completed for: `{file_path}`"

    elif latest_result.get('task_type') == 'system_info':
        system_info = output_data.get('system_info', '')
        return f"Here's your system information:\n\n```\n{system_info}\n```"

    # Default response
    progress = workflow_result.get('final_progress', 0)
    iterations = workflow_result.get('iterations', 1)

    return f"I processed your request through {iterations} iteration(s) with {progress:.1%} completion. The task has been executed successfully."

async def try_handle_enhanced_commands(user_input: str) -> bool:
    """Handle enhanced commands specific to advanced features."""
    if user_input.strip() == "/status":
        show_system_status()
        return True
    elif user_input.strip() == "/performance":
        show_enhanced_performance_metrics()
        return True
    elif user_input.strip() == "/models":
        show_model_health_status()
        return True
    elif user_input.strip() == "/codebase":
        show_codebase_summary()
        return True
    elif user_input.strip() == "/intelligence":
        show_intelligence_metrics()
        return True
    elif user_input.strip() == "/cache":
        show_cache_statistics()
        return True
    elif user_input.strip() == "/optimize":
        optimize_performance()
        return True
    elif user_input.strip() == "/mcp-status":
        show_mcp_integration_status()
        return True
    return False

def show_mcp_integration_status():
    """Show MCP integration status in the enhanced dashboard."""
    console.print("\n[bold bright_blue]🔌 MCP Integration Status[/bold bright_blue]")

    # MCP client status
    mcp_status = mcp_client.get_server_status()
    connected_servers = sum(1 for status in mcp_status.values() if status["status"] == "connected")
    total_servers = len(mcp_status)

    integration_table = Table(title="MCP Integration Overview", show_header=True, header_style="bold cyan")
    integration_table.add_column("Component", style="cyan", width=25)
    integration_table.add_column("Status", style="green", width=20)
    integration_table.add_column("Details", style="white", width=40)

    # MCP Client Status
    client_status = "🟢 Active" if mcp_client else "🔴 Inactive"
    integration_table.add_row("MCP Client", client_status, f"Managing {total_servers} server configurations")

    # Server Connectivity
    if total_servers > 0:
        connectivity_status = f"🟢 {connected_servers}/{total_servers} Connected" if connected_servers > 0 else "🔴 No Connections"
        integration_table.add_row("Server Connectivity", connectivity_status, f"{connected_servers} active, {total_servers - connected_servers} inactive")
    else:
        integration_table.add_row("Server Connectivity", "🟡 No Servers", "Use '/mcp config' to add servers")

    # Configuration File
    config_status = "🟢 Found" if mcp_client.config_file.exists() else "🔴 Missing"
    integration_table.add_row("Configuration", config_status, str(mcp_client.config_file))

    console.print(integration_table)

    # Show available MCP capabilities
    if connected_servers > 0:
        console.print("\n[bold green]🚀 Available MCP Capabilities:[/bold green]")
        for name, status in mcp_status.items():
            if status["status"] == "connected":
                console.print(f"  • {name}: Ready for requests")

    console.print("\n[bold yellow]💡 Quick Actions:[/bold yellow]")
    console.print("  • Use '/mcp status' for detailed server information")
    console.print("  • Use '/mcp start' to connect all servers")
    console.print("  • Use '/mcp config' to modify server configuration")

def show_enhanced_performance_metrics() -> None:
    """Show comprehensive performance metrics with enhanced UI."""
    console.print("\n[bold bright_blue]⚡ Enhanced Performance Dashboard[/bold bright_blue]")

    # Get metrics from performance optimizer
    metrics = performance_optimizer.get_performance_metrics()

    # Create performance table
    perf_table = Table(title="Performance Metrics", show_header=True, header_style="bold cyan")
    perf_table.add_column("Metric", style="cyan", width=25)
    perf_table.add_column("Value", style="green", width=15)
    perf_table.add_column("Status", style="yellow", width=15)

    # Cache performance
    cache_status = "🟢 Excellent" if metrics["cache_hit_rate"] > 0.7 else "🟡 Good" if metrics["cache_hit_rate"] > 0.4 else "🔴 Poor"
    perf_table.add_row("Cache Hit Rate", f"{metrics['cache_hit_rate']:.1%}", cache_status)

    # Response time
    response_status = "🟢 Fast" if metrics["avg_response_time"] < 2.0 else "🟡 Moderate" if metrics["avg_response_time"] < 5.0 else "🔴 Slow"
    perf_table.add_row("Avg Response Time", f"{metrics['avg_response_time']:.2f}s", response_status)

    # API calls
    perf_table.add_row("Total API Calls", str(metrics["total_api_calls"]), "📊 Tracking")
    perf_table.add_row("Cache Size", str(metrics["cache_size"]), "💾 Active")
    perf_table.add_row("Total Requests", str(metrics["total_requests"]), "📈 Growing")

    console.print(perf_table)

    # Show optimization suggestions
    console.print("\n[bold yellow]💡 Performance Optimization Suggestions:[/bold yellow]")
    if metrics["cache_hit_rate"] < 0.5:
        console.print("  • Consider using similar queries to improve cache efficiency")
    if metrics["avg_response_time"] > 3.0:
        console.print("  • Try switching to faster models like mistral-small or gemini-1.5-flash")
    if metrics["cache_size"] > 800:
        console.print("  • Cache is getting large, consider clearing with /cache clear")

    console.print("  • Use /optimize to automatically optimize performance")

def show_model_health_status() -> None:
    """Show model health and performance status."""
    console.print("\n[bold bright_blue]🏥 Model Health Dashboard[/bold bright_blue]")

    health_table = Table(title="Model Health Status", show_header=True, header_style="bold cyan")
    health_table.add_column("Model", style="cyan", width=20)
    health_table.add_column("Status", style="green", width=12)
    health_table.add_column("Avg Latency", style="yellow", width=12)
    health_table.add_column("Error Rate", style="red", width=12)
    health_table.add_column("Provider", style="blue", width=10)

    for model, health in model_router.model_health.items():
        status_emoji = {
            "healthy": "🟢 Healthy",
            "degraded": "🟡 Degraded",
            "unhealthy": "🔴 Unhealthy"
        }.get(health["status"], "❓ Unknown")

        provider = MODEL_PROVIDERS.get(model, "Unknown")
        provider_emoji = {
            "deepseek": "🚀",
            "gemini": "🌟",
            "mistral": "🔥"
        }.get(provider, "❓")

        health_table.add_row(
            model,
            status_emoji,
            f"{health['avg_latency']:.2f}s",
            f"{health['error_rate']:.1%}",
            f"{provider_emoji} {provider.title()}"
        )

    console.print(health_table)

def show_cache_statistics() -> None:
    """Show detailed cache statistics."""
    console.print("\n[bold bright_blue]💾 Cache Statistics[/bold bright_blue]")

    metrics = performance_optimizer.get_performance_metrics()

    cache_table = Table(title="Cache Performance", show_header=True, header_style="bold cyan")
    cache_table.add_column("Metric", style="cyan")
    cache_table.add_column("Value", style="green")

    cache_table.add_row("Cache Size", f"{metrics['cache_size']} entries")
    cache_table.add_row("Hit Rate", f"{metrics['cache_hit_rate']:.1%}")
    cache_table.add_row("Total Hits", str(performance_optimizer.metrics["cache_hits"]))
    cache_table.add_row("Total Misses", str(performance_optimizer.metrics["cache_misses"]))

    console.print(cache_table)

    console.print("\n[bold yellow]Cache Management:[/bold yellow]")
    console.print("  • Use '/cache clear' to clear cache")
    console.print("  • Use '/cache stats' to see detailed statistics")

def optimize_performance() -> None:
    """Automatically optimize performance settings."""
    console.print("\n[bold bright_blue]🔧 Auto-Optimizing Performance...[/bold bright_blue]")

    metrics = performance_optimizer.get_performance_metrics()
    optimizations = []

    # Clear cache if it's too large
    if metrics["cache_size"] > 500:
        performance_optimizer.response_cache.clear()
        optimizations.append("✓ Cleared large cache")

    # Reset model health if error rates are high
    unhealthy_models = [model for model, health in model_router.model_health.items()
                       if health["error_rate"] > 0.3]

    if unhealthy_models:
        for model in unhealthy_models:
            model_router.model_health[model]["status"] = "healthy"
            model_router.model_health[model]["error_rate"] = 0.0
        optimizations.append(f"✓ Reset health for {len(unhealthy_models)} models")

    # Show results
    if optimizations:
        console.print("\n[bold green]Optimizations Applied:[/bold green]")
        for opt in optimizations:
            console.print(f"  {opt}")
    else:
        console.print("\n[bold green]✓ System is already optimized![/bold green]")

    console.print("\n[dim]Performance optimization complete.[/dim]")

def show_system_status() -> None:
    """Show comprehensive system status."""
    console.print("\n[bold bright_blue]🔍 CODY System Status[/bold bright_blue]")

    # Module status
    status_table = Table(title="Module Status")
    status_table.add_column("Module", style="cyan")
    status_table.add_column("Status", style="green")
    status_table.add_column("Details", style="dim")

    modules = [
        ("NLP Processor", "✅ Active" if nlp_processor else "❌ Inactive", "Natural Language Processing"),
        ("Code Analyzer", "✅ Active" if code_analyzer else "❌ Inactive", "AST-based Code Analysis"),
        ("Autonomous Debugger", "✅ Active" if autonomous_debugger else "❌ Inactive", "Error Detection & Fixing"),
        ("Web Search RAG", "✅ Active" if web_search_rag else "❌ Inactive", "Real-time Information Retrieval"),
        ("Task Manager", "✅ Active" if task_manager else "❌ Inactive", "Multi-threaded Execution"),
        ("General Intelligence", "✅ Active" if general_intelligence else "❌ Inactive", "Chain-of-Thought Reasoning"),
        ("Performance Core", "✅ Active" if performance_core else "❌ Inactive", "Ultra-low Latency Processing"),
        ("Codebase Awareness", "✅ Active" if codebase_awareness else "❌ Inactive", "Full Project Understanding"),
        ("Terminal FS Agent", "✅ Active" if terminal_fs_agent else "❌ Inactive", "Command & File Operations")
    ]

    for module, status, details in modules:
        status_table.add_row(module, status, details)

    console.print(status_table)

def show_performance_metrics() -> None:
    """Show performance metrics."""
    console.print("\n[bold bright_blue]⚡ Performance Metrics[/bold bright_blue]")

    if performance_core:
        stats = performance_core.get_performance_stats()

        metrics_table = Table(title="Performance Statistics")
        metrics_table.add_column("Metric", style="cyan")
        metrics_table.add_column("Value", style="green")

        metrics_table.add_row("Cache Hit Rate", f"{stats['cache_stats']['hit_rate']:.2%}")
        metrics_table.add_row("Cache Size", f"{stats['cache_stats']['size_mb']:.1f} MB")
        metrics_table.add_row("CPU Usage", f"{stats['system_stats']['cpu_percent']:.1f}%")
        metrics_table.add_row("Memory Usage", f"{stats['system_stats']['memory_percent']:.1f}%")
        metrics_table.add_row("Active Threads", str(stats['system_stats']['active_threads']))

        console.print(metrics_table)
    else:
        console.print("[yellow]Performance core not available[/yellow]")

def show_codebase_summary() -> None:
    """Show codebase summary."""
    console.print("\n[bold bright_blue]📁 Codebase Summary[/bold bright_blue]")

    if codebase_awareness:
        summary = codebase_awareness.get_project_summary()

        summary_table = Table(title="Project Overview")
        summary_table.add_column("Aspect", style="cyan")
        summary_table.add_column("Details", style="green")

        summary_table.add_row("Total Files", str(summary['project_structure']['total_files']))
        summary_table.add_row("Active Files", str(summary['active_files']))
        summary_table.add_row("Recent Changes", str(summary['recent_changes']))
        summary_table.add_row("Languages", ", ".join(summary['languages_used']))
        summary_table.add_row("Total Functions", str(summary['total_functions']))
        summary_table.add_row("Total Classes", str(summary['total_classes']))

        console.print(summary_table)
    else:
        console.print("[yellow]Codebase awareness not available[/yellow]")

def show_intelligence_metrics() -> None:
    """Show intelligence system metrics."""
    console.print("\n[bold bright_blue]🧠 Intelligence Metrics[/bold bright_blue]")

    if general_intelligence:
        metrics = general_intelligence.get_intelligence_metrics()

        if metrics.get("status") == "no_data":
            console.print("[yellow]No intelligence data available yet[/yellow]")
        else:
            intel_table = Table(title="Intelligence Performance")
            intel_table.add_column("Metric", style="cyan")
            intel_table.add_column("Value", style="green")

            intel_table.add_row("Total Executions", str(metrics['total_executions']))
            intel_table.add_row("Recent Success Rate", f"{metrics['recent_success_rate']:.2%}")
            intel_table.add_row("Reasoning Quality", f"{metrics['average_reasoning_quality']:.2f}")
            intel_table.add_row("Memory Categories", ", ".join(metrics['memory_categories']))

            console.print(intel_table)
    else:
        console.print("[yellow]General intelligence not available[/yellow]")

# -----------------------------------------------------------------------------
# 12. MAIN LOOP & ENTRY POINT
# -----------------------------------------------------------------------------

def main_loop() -> None:
    """Main application loop."""
    global conversation_history

    while True:
        try:
            prompt_indicator = get_enhanced_prompt_indicator()
            user_input = prompt_session.prompt(f"{prompt_indicator} You: ")
            
            if not user_input.strip(): 
                continue

            # Handle commands
            if try_handle_add_command(user_input): continue
            if try_handle_commit_command(user_input): continue
            if try_handle_git_command(user_input): continue
            if try_handle_git_info_command(user_input): continue
            if try_handle_r1_command(user_input): continue
            if try_handle_reasoner_command(user_input): continue
            if try_handle_switch_command(user_input): continue  # NEW: Model switching like Augment Agent
            if try_handle_clear_command(user_input): continue
            if try_handle_clear_context_command(user_input): continue
            if try_handle_context_command(user_input): continue
            if try_handle_folder_command(user_input): continue
            if try_handle_exit_command(user_input): continue
            if try_handle_help_command(user_input): continue
            if asyncio.run(try_handle_mcp_commands(user_input)): continue

            # Handle enhanced commands if available
            if ADVANCED_MODULES_AVAILABLE:
                if asyncio.run(try_handle_enhanced_commands(user_input)): continue

            # INTELLIGENT CONVERSATION HANDLING LIKE AUGMENT AGENT
            # Analyze user intent and automatically choose tools
            intent_analysis = analyze_user_intent(user_input)

            # Pre-process: Automatically execute tools based on intent
            tool_results = ""
            if intent_analysis.get('should_use_tools'):
                console.print(f"[dim]🧠 Detected {intent_analysis['intent_type']} intent (confidence: {intent_analysis['confidence']:.1%})[/dim]")
                tool_results = execute_intelligent_tools(user_input, intent_analysis, {})
                if tool_results:
                    console.print(f"[dim]🛠️ Automatic tool execution completed[/dim]")

            # Enhance user input with tool results for better context
            enhanced_user_input = user_input
            if tool_results:
                enhanced_user_input = f"{user_input}\n\n[AUTOMATIC TOOL RESULTS]\n{tool_results}"

            # Add enhanced user message to conversation
            conversation_history.append({"role": "user", "content": enhanced_user_input})

            # Enhanced workflow processing if available
            enhanced_keywords = [
                'create', 'file', 'debug', 'search', 'analyze', 'install', 'run',
                'show', 'display', 'get', 'date', 'time', 'current', 'directory',
                'list', 'files', 'pwd', 'ls', 'dir', 'terminal', 'command',
                'execute', 'system', 'info', 'status'
            ]

            if ADVANCED_MODULES_AVAILABLE and any(word in user_input.lower() for word in enhanced_keywords):
                try:
                    console.print("[dim]🚀 Using Enhanced Iterative Workflow Engine...[/dim]")
                    asyncio.run(process_enhanced_workflow(enhanced_user_input, conversation_history))
                    continue  # Skip normal processing
                except Exception as e:
                    console.print(f"[yellow]Enhanced workflow failed, falling back to standard processing: {e}[/yellow]")
                    logger.warning(f"Enhanced workflow error: {e}")

            # Check context usage and warn if necessary
            context_info = get_context_usage_info()
            if context_info["critical_limit"] and len(conversation_history) % 10 == 0:  # Warn every 10 messages when critical
                console.print(f"[red]⚠ Context critical: {context_info['token_usage_percent']:.1f}% used. Consider /clear-context or /context for details.[/red]")
            elif context_info["approaching_limit"] and len(conversation_history) % 20 == 0:  # Warn every 20 messages when approaching
                console.print(f"[yellow]⚠ Context high: {context_info['token_usage_percent']:.1f}% used. Use /context for details.[/yellow]")
            
            # Determine which model to use
            current_model = agent_state.model_context['current_model']

            # Use robust API call with automatic fallbacks
            try:
                model_name = get_model_display_name(current_model)

                with console.status(f"[bold yellow]{model_name} is thinking...[/bold yellow]", spinner="dots"):

                    # Handle Gemini models
                    if current_model.startswith('gemini') and gemini_client:
                        try:
                            response = make_gemini_api_call(conversation_history, temperature=0.7, max_tokens=2048)

                            # Display Gemini response
                            response_text = response.text if hasattr(response, 'text') else str(response)
                            console.print(f"[bold bright_magenta]🤖 {model_name}:[/bold bright_magenta] {response_text}")

                            # Enhance with intelligence
                            enhanced_response = enhance_response_with_intelligence(
                                response_text, intent_analysis, user_input
                            )

                            # Add to conversation history
                            conversation_history.append({"role": "assistant", "content": enhanced_response})

                            if enhanced_response != response_text:
                                console.print(enhanced_response[len(response_text):])

                            continue  # Skip DeepSeek processing

                        except Exception as e:
                            console.print(f"[red]Gemini error: {e}[/red]")
                            console.print("[yellow]Falling back to DeepSeek...[/yellow]")
                            current_model = FALLBACK_MODEL

                    # Use optimized API call with enhanced error handling
                    task_type = determine_task_type(user_input)

                    try:
                        console.print(f"[dim]🚀 Making API call with model: {current_model}[/dim]")
                        response_stream = make_optimized_api_call(
                            model=current_model,
                            messages=conversation_history,
                            tools=tools,
                            stream=True,
                            temperature=0.7,
                            max_tokens=2048,
                            task_type=task_type
                        )
                    except Exception as api_error:
                        console.print(f"[red]❌ API call failed: {api_error}[/red]")
                        console.print(f"[yellow]🔄 Trying fallback model: {FALLBACK_MODEL}[/yellow]")

                        # Try with fallback model
                        response_stream = make_optimized_api_call(
                            model=FALLBACK_MODEL,
                            messages=conversation_history,
                            tools=tools,
                            stream=True,
                            temperature=0.7,
                            max_tokens=2048,
                            task_type=task_type
                        )

            except Exception as e:
                console.print(f"[red]API Error: {e}[/red]")
                console.print("[yellow]Using fallback response generation...[/yellow]")

                # Fallback response
                fallback_response = generate_fallback_response(user_input, intent_analysis)
                conversation_history.append({"role": "assistant", "content": fallback_response})
                console.print(f"[bold bright_magenta]🤖 CODY (Fallback):[/bold bright_magenta] {fallback_response}")
                continue
            
            # Process streaming response
            full_response_content = ""
            accumulated_tool_calls: List[Dict[str, Any]] = []

            console.print(f"[bold bright_magenta]🤖 {model_name}:[/bold bright_magenta] ", end="")
            for chunk in response_stream:
                delta: ChoiceDelta = chunk.choices[0].delta
                if delta.content:
                    content_part = delta.content
                    console.print(content_part, end="", style="bright_magenta")
                    full_response_content += content_part
                
                if delta.tool_calls:
                    for tool_call_chunk in delta.tool_calls:
                        idx = tool_call_chunk.index
                        while len(accumulated_tool_calls) <= idx:
                            accumulated_tool_calls.append({"id": "", "type": "function", "function": {"name": "", "arguments": ""}})
                        
                        current_tool_dict = accumulated_tool_calls[idx]
                        if tool_call_chunk.id: 
                            current_tool_dict["id"] = tool_call_chunk.id
                        if tool_call_chunk.function:
                            if tool_call_chunk.function.name: 
                                current_tool_dict["function"]["name"] = tool_call_chunk.function.name
                            if tool_call_chunk.function.arguments: 
                                current_tool_dict["function"]["arguments"] += tool_call_chunk.function.arguments
            console.print()

            # Always add assistant message to maintain conversation flow
            assistant_message: Dict[str, Any] = {"role": "assistant"}
            
            # Always include content (even if empty) to maintain conversation flow
            assistant_message["content"] = full_response_content

            # Validate and add tool calls if any
            valid_tool_calls = validate_tool_calls(accumulated_tool_calls)
            if valid_tool_calls:
                assistant_message["tool_calls"] = valid_tool_calls
            
            # Enhance response with intelligence like Augment Agent
            if full_response_content and not valid_tool_calls:
                enhanced_response = enhance_response_with_intelligence(
                    full_response_content, intent_analysis, user_input
                )
                assistant_message["content"] = enhanced_response

            # Always add the assistant message (maintains conversation flow)
            conversation_history.append(assistant_message)

            # Execute tool calls and allow assistant to continue naturally
            if valid_tool_calls:
                # Execute all tool calls first
                for tool_call_to_exec in valid_tool_calls: 
                    console.print(Panel(
                        f"[bold blue]Calling:[/bold blue] {tool_call_to_exec['function']['name']}\n"
                        f"[bold blue]Args:[/bold blue] {tool_call_to_exec['function']['arguments']}",
                        title="🛠️ Function Call", border_style="yellow", expand=False
                    ))
                    tool_output = execute_function_call_dict(tool_call_to_exec) 
                    console.print(Panel(tool_output, title=f"↪️ Output of {tool_call_to_exec['function']['name']}", border_style="green", expand=False))
                    conversation_history.append({
                        "role": "tool",
                        "tool_call_id": tool_call_to_exec["id"],
                        "name": tool_call_to_exec["function"]["name"],
                        "content": tool_output 
                    })
                
                # Now let the assistant continue with the tool results
                # This creates a natural conversation flow where the assistant processes the results
                max_continuation_rounds = 3
                current_round = 0
                
                while current_round < max_continuation_rounds:
                    current_round += 1
                    
                    with console.status(f"[bold yellow]{model_name} is processing results...[/bold yellow]", spinner="dots"):
                        continue_response_stream: Stream[ChatCompletionChunk] = client.chat.completions.create(
                            model=current_model,
                            messages=conversation_history, # type: ignore 
                            tools=tools, # type: ignore 
                            tool_choice="auto",
                            stream=True
                        )
                    
                    # Process the continuation response
                    continuation_content = ""
                    continuation_tool_calls: List[Dict[str, Any]] = []
                    
                    console.print(f"[bold bright_magenta]🤖 {model_name}:[/bold bright_magenta] ", end="")
                    for chunk in continue_response_stream:
                        delta: ChoiceDelta = chunk.choices[0].delta
                        if delta.content:
                            content_part = delta.content
                            console.print(content_part, end="", style="bright_magenta")
                            continuation_content += content_part
                        
                        if delta.tool_calls:
                            for tool_call_chunk in delta.tool_calls:
                                idx = tool_call_chunk.index
                                while len(continuation_tool_calls) <= idx:
                                    continuation_tool_calls.append({"id": "", "type": "function", "function": {"name": "", "arguments": ""}})
                                
                                current_tool_dict = continuation_tool_calls[idx]
                                if tool_call_chunk.id: 
                                    current_tool_dict["id"] = tool_call_chunk.id
                                if tool_call_chunk.function:
                                    if tool_call_chunk.function.name: 
                                        current_tool_dict["function"]["name"] = tool_call_chunk.function.name
                                    if tool_call_chunk.function.arguments: 
                                        current_tool_dict["function"]["arguments"] += tool_call_chunk.function.arguments
                    console.print()
                    
                    # Add the continuation response to conversation history
                    continuation_message: Dict[str, Any] = {"role": "assistant", "content": continuation_content}
                    
                    # Check if there are more tool calls to execute
                    valid_continuation_tools = validate_tool_calls(continuation_tool_calls)
                    if valid_continuation_tools:
                        continuation_message["tool_calls"] = valid_continuation_tools
                        conversation_history.append(continuation_message)
                        
                        # Execute the additional tool calls
                        for tool_call_to_exec in valid_continuation_tools:
                            console.print(Panel(
                                f"[bold blue]Calling:[/bold blue] {tool_call_to_exec['function']['name']}\n"
                                f"[bold blue]Args:[/bold blue] {tool_call_to_exec['function']['arguments']}",
                                title="🛠️ Function Call", border_style="yellow", expand=False
                            ))
                            tool_output = execute_function_call_dict(tool_call_to_exec)
                            console.print(Panel(tool_output, title=f"↪️ Output of {tool_call_to_exec['function']['name']}", border_style="green", expand=False))
                            conversation_history.append({
                                "role": "tool",
                                "tool_call_id": tool_call_to_exec["id"],
                                "name": tool_call_to_exec["function"]["name"],
                                "content": tool_output
                            })
                        
                        # Continue the loop to let assistant process these new results
                        continue
                    else:
                        # No more tool calls, add the final response and break
                        conversation_history.append(continuation_message)
                        break
                
                # If we hit the max rounds, warn about it
                if current_round >= max_continuation_rounds:
                    console.print(f"[yellow]⚠ Reached maximum continuation rounds ({max_continuation_rounds}). Conversation continues.[/yellow]")
            
            # Smart truncation that preserves tool call sequences
            conversation_history = smart_truncate_history(conversation_history, MAX_HISTORY_MESSAGES)

        except KeyboardInterrupt: 
            console.print("\n[yellow]⚠ Interrupted. Ctrl+D or /exit to quit.[/yellow]")
        except EOFError: 
            console.print("[blue]👋 Goodbye! (EOF)[/blue]")
            sys.exit(0)
        except Exception as e:
            console.print(f"\n[red]✗ Unexpected error in main loop:[/red]")
            console.print_exception(width=None, extra_lines=1, show_locals=True)

def initialize_application() -> None:
    """Initialize the application and check for existing git repository."""
    if Path(".git").exists() and Path(".git").is_dir():
        agent_state.git_context['enabled'] = True
        try:
            res = subprocess.run(["git", "branch", "--show-current"], cwd=str(Path.cwd()), capture_output=True, text=True, check=False)
            if res.returncode == 0 and res.stdout.strip():
                agent_state.git_context['branch'] = res.stdout.strip()
            else:
                init_branch_res = subprocess.run(["git", "config", "init.defaultBranch"], cwd=str(Path.cwd()), capture_output=True, text=True)
                agent_state.git_context['branch'] = init_branch_res.stdout.strip() if init_branch_res.returncode == 0 and init_branch_res.stdout.strip() else "main"
        except FileNotFoundError:
            console.print("[yellow]Git not found. Git features disabled.[/yellow]")
            agent_state.git_context['enabled'] = False
        except Exception as e:
            console.print(f"[yellow]Could not get Git branch: {e}.[/yellow]")

def get_directory_tree_summary(root_dir: Path, max_depth: int = 3, max_entries: int = 100) -> str:
    """
    Generate a summary of the directory tree up to a certain depth and entry count.
    """
    lines = []
    entry_count = 0

    def walk(dir_path: Path, prefix: str = "", depth: int = 0):
        nonlocal entry_count
        if depth > max_depth or entry_count >= max_entries:
            return
        try:
            entries = sorted([e for e in dir_path.iterdir() if not e.name.startswith('.')])
        except Exception:
            return
        for entry in entries:
            if entry_count >= max_entries:
                lines.append(f"{prefix}... (truncated)")
                return
            if entry.is_dir():
                lines.append(f"{prefix}{entry.name}/")
                entry_count += 1
                walk(entry, prefix + "  ", depth + 1)
            else:
                lines.append(f"{prefix}{entry.name}")
                entry_count += 1

    walk(root_dir)
    return "\n".join(lines)

def generate_intelligent_response(user_input: str, context: Dict = None) -> str:
    """
    Generate intelligent response using enhanced reasoning and context.

    Args:
        user_input: User's input/request
        context: Additional context information

    Returns:
        Intelligent response with suggestions and reasoning
    """
    try:
        # Use reasoning engine to analyze the request
        if 'reasoning_engine' in globals() and reasoning_engine:
            reasoning_result = reasoning_engine.reason_through_problem(user_input, context)

            # Get intelligent suggestions
            suggestions = []
            if 'suggestion_engine' in globals() and suggestion_engine:
                suggestions = suggestion_engine.get_suggestions(user_input, context)

            # Build enhanced response
            response_parts = []

            # Add reasoning chain if complex problem
            if reasoning_result.get('steps') and len(reasoning_result['steps']) > 1:
                response_parts.append("🧠 **Analysis & Reasoning:**")
                for step in reasoning_result['steps']:
                    response_parts.append(f"   • {step['description']}")
                response_parts.append("")

            # Add main solution
            solution = reasoning_result.get('solution', {})
            if solution:
                response_parts.append(f"🎯 **Recommended Approach:** {solution.get('approach', 'direct')}")
                response_parts.append(f"   {solution.get('description', 'Direct implementation')}")
                response_parts.append(f"   Confidence: {solution.get('confidence', 0.8):.1%}")
                response_parts.append("")

            # Add intelligent suggestions
            if suggestions:
                response_parts.append("💡 **Smart Suggestions:**")
                for suggestion in suggestions[:5]:  # Limit to top 5
                    response_parts.append(f"   • {suggestion}")
                response_parts.append("")

            return "\n".join(response_parts)

        else:
            return "I'll help you with that request using my advanced capabilities."

    except Exception as e:
        logger.error(f"Error in intelligent response generation: {e}")
        return "I'll assist you with your request using my comprehensive toolkit."

def enhance_function_call_response(function_name: str, result: str, context: Dict = None) -> str:
    """
    Enhance function call responses with intelligent insights.

    Args:
        function_name: Name of the function that was called
        result: Original function result
        context: Additional context

    Returns:
        Enhanced response with insights and suggestions
    """
    try:
        enhanced_parts = [result]

        # Add function-specific insights
        if 'suggestion_engine' in globals() and suggestion_engine:
            next_actions = suggestion_engine.suggest_next_actions(function_name, context)
            if next_actions:
                enhanced_parts.append("\n💡 **Suggested Next Steps:**")
                for action in next_actions[:3]:
                    enhanced_parts.append(f"   • {action}")

        # Add context-specific recommendations
        if 'search' in function_name and 'No results found' in result:
            enhanced_parts.append("\n🔍 **Search Tips:**")
            enhanced_parts.append("   • Try using semantic search mode")
            enhanced_parts.append("   • Use fuzzy search for typo tolerance")
            enhanced_parts.append("   • Build file index for better results")

        if 'create' in function_name or 'edit' in function_name:
            enhanced_parts.append("\n🛡️ **Safety Recommendations:**")
            enhanced_parts.append("   • Consider running tests after changes")
            enhanced_parts.append("   • Use git_commit to save your progress")
            enhanced_parts.append("   • Run analyze_code to check quality")

        return "\n".join(enhanced_parts)

    except Exception as e:
        logger.error(f"Error enhancing function call response: {e}")
        return result

def validate_and_get_model(model_name: str) -> str:
    """
    Enhanced model validation with comprehensive error handling and provider-specific validation.
    """
    try:
        console.print(f"[dim]🔍 Validating model: {model_name}[/dim]")

        # Direct model mapping first
        if model_name in MODEL_MAPPING:
            api_model = MODEL_MAPPING[model_name]
            provider = MODEL_PROVIDERS.get(model_name, "unknown")

            console.print(f"[dim]✅ Model mapped: {model_name} → {api_model} (provider: {provider})[/dim]")

            # Provider-specific availability checks
            if provider == "gemini" and not GEMINI_AVAILABLE:
                console.print(f"[yellow]⚠ Gemini not available, falling back to {FALLBACK_MODEL}[/yellow]")
                return FALLBACK_MODEL
            elif provider == "mistral" and not MISTRAL_AVAILABLE:
                console.print(f"[yellow]⚠ Mistral not available, falling back to {FALLBACK_MODEL}[/yellow]")
                return FALLBACK_MODEL

            return api_model

        # Handle direct API model names
        valid_deepseek_models = ["deepseek-chat", "deepseek-reasoner", "deepseek-coder"]
        valid_gemini_models = ["gemini-2.0-flash-exp", "gemini-1.5-flash", "gemini-1.5-pro"]
        valid_mistral_models = ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "codestral-latest"]

        all_valid_models = valid_deepseek_models + valid_gemini_models + valid_mistral_models

        if model_name in all_valid_models:
            console.print(f"[dim]✅ Direct API model validated: {model_name}[/dim]")

            # Check provider availability for direct API models
            if model_name.startswith("gemini") and not GEMINI_AVAILABLE:
                console.print(f"[yellow]⚠ Gemini not available, falling back to {FALLBACK_MODEL}[/yellow]")
                return FALLBACK_MODEL
            elif model_name.startswith("mistral") and not MISTRAL_AVAILABLE:
                console.print(f"[yellow]⚠ Mistral not available, falling back to {FALLBACK_MODEL}[/yellow]")
                return FALLBACK_MODEL

            return model_name

        # Handle common variations and typos
        model_lower = model_name.lower().strip()

        if "deepseek" in model_lower:
            if "chat" in model_lower or model_lower == "deepseek":
                console.print(f"[dim]🔄 Interpreting '{model_name}' as deepseek-chat[/dim]")
                return "deepseek-chat"
            elif "reason" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as deepseek-reasoner[/dim]")
                return "deepseek-reasoner"

        elif "gemini" in model_lower and GEMINI_AVAILABLE:
            if "2.0" in model_lower or "flash" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as gemini-2.0-flash-exp[/dim]")
                return "gemini-2.0-flash-exp"
            elif "1.5" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as gemini-1.5-flash[/dim]")
                return "gemini-1.5-flash"

        elif "mistral" in model_lower and MISTRAL_AVAILABLE:
            if "large" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as mistral-large-latest[/dim]")
                return "mistral-large-latest"
            elif "medium" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as mistral-medium-latest[/dim]")
                return "mistral-medium-latest"
            elif "small" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as mistral-small-latest[/dim]")
                return "mistral-small-latest"
            elif "codestral" in model_lower:
                console.print(f"[dim]🔄 Interpreting '{model_name}' as codestral-latest[/dim]")
                return "codestral-latest"

        # Final fallback with helpful message
        console.print(f"[red]❌ Unknown model '{model_name}', using fallback: {FALLBACK_MODEL}[/red]")
        console.print(f"[dim]💡 Available models: {', '.join(list(MODEL_MAPPING.keys())[:5])}...[/dim]")
        console.print(f"[dim]💡 Use '/switch' to see all available models[/dim]")

        return FALLBACK_MODEL

    except Exception as e:
        console.print(f"[red]❌ Model validation error: {e}[/red]")
        console.print(f"[dim]🔄 Using fallback model: {FALLBACK_MODEL}[/dim]")
        return FALLBACK_MODEL

def make_optimized_api_call(model: str, messages: List[Dict], tools: List = None, **kwargs):
    """
    Make optimized API call with caching, performance monitoring, and multi-provider support.
    """
    start_time = time.time()

    # Generate cache key
    cache_key = performance_optimizer.cache_key(model, messages, **kwargs)

    # Check cache first
    cached_response = performance_optimizer.get_cached_response(cache_key)
    if cached_response and not kwargs.get('force_refresh', False):
        console.print("[dim]⚡ Using cached response[/dim]")
        return cached_response

    # Select optimal model using intelligent routing
    task_type = kwargs.get('task_type', 'general')
    optimal_model = model_router.select_optimal_model(model, task_type)

    if optimal_model != model:
        console.print(f"[dim]🔄 Routing {model} → {optimal_model} for better performance[/dim]")

    max_retries = PERFORMANCE_CONFIG["max_retries"]
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Validate model with enhanced error handling
            validated_model = validate_and_get_model(optimal_model)
            provider = MODEL_PROVIDERS.get(optimal_model, "unknown")  # Use original model name for provider lookup

            console.print(f"[dim]🎯 Using model: {validated_model} (provider: {provider})[/dim]")

            # Route to appropriate provider
            if provider == "mistral" and MISTRAL_AVAILABLE:
                response = make_mistral_api_call(validated_model, messages, **kwargs)
            elif provider == "gemini" and GEMINI_AVAILABLE:
                response = make_gemini_api_call(messages, **kwargs)
            elif provider == "deepseek" and client:
                response = make_deepseek_api_call(validated_model, messages, tools, **kwargs)
            else:
                raise Exception(f"No available provider for model: {validated_model}")

            # Update performance metrics
            latency = time.time() - start_time
            model_router.update_model_health(optimal_model, latency, True)
            performance_optimizer.metrics["api_calls"] += 1
            performance_optimizer.metrics["total_requests"] += 1

            # Update average response time
            current_avg = performance_optimizer.metrics["avg_response_time"]
            total_requests = performance_optimizer.metrics["total_requests"]
            performance_optimizer.metrics["avg_response_time"] = (
                (current_avg * (total_requests - 1) + latency) / total_requests
            )

            # Cache successful response
            if hasattr(response, 'text'):
                response_text = response.text
            elif hasattr(response, 'choices') and response.choices:
                response_text = response.choices[0].message.content
            else:
                response_text = str(response)

            performance_optimizer.cache_response(cache_key, response_text)

            return response

        except Exception as e:
            retry_count += 1
            latency = time.time() - start_time
            model_router.update_model_health(optimal_model, latency, False)

            error_msg = str(e).lower()

            # Handle specific error types with intelligent fallbacks
            if "model" in error_msg and "not" in error_msg and "exist" in error_msg:
                console.print(f"[yellow]⚠ Model '{optimal_model}' not found, trying fallback...[/yellow]")
                optimal_model = model_router.select_optimal_model(FALLBACK_MODEL, task_type)
            elif "rate limit" in error_msg:
                wait_time = min(2 ** retry_count, 10)  # Exponential backoff
                console.print(f"[yellow]⚠ Rate limited, waiting {wait_time}s...[/yellow]")
                time.sleep(wait_time)
            elif "timeout" in error_msg:
                console.print(f"[yellow]⚠ Request timeout, retrying... ({retry_count}/{max_retries})[/yellow]")
            else:
                console.print(f"[yellow]⚠ API error: {e}, retrying... ({retry_count}/{max_retries})[/yellow]")

            if retry_count >= max_retries:
                raise Exception(f"API call failed after {max_retries} retries: {e}")

def make_mistral_api_call(model: str, messages: List[Dict], **kwargs):
    """Make optimized Mistral AI API call using requests library with key rotation and error handling."""
    global mistral_requests_client, mistral_working_keys

    try:
        if not mistral_requests_client or not MISTRAL_AVAILABLE:
            raise Exception("Mistral client not available")

        # Convert messages to standard format (no special imports needed)
        mistral_messages = []
        for msg in messages[-10:]:  # Use last 10 messages for context
            if msg['role'] == 'system':
                mistral_messages.append({"role": "system", "content": msg['content']})
            elif msg['role'] == 'user':
                mistral_messages.append({"role": "user", "content": msg['content']})
            elif msg['role'] == 'assistant':
                mistral_messages.append({"role": "assistant", "content": msg.get('content', 'I understand.')})

        # Ensure we have at least one message
        if not mistral_messages:
            mistral_messages.append({"role": "user", "content": "Hello"})

        # Map our model names to Mistral's expected names
        mistral_model_map = {
            "mistral-large": "mistral-large-latest",
            "mistral-medium": "mistral-medium-latest",
            "mistral-small": "mistral-small-latest",
            "mistral-codestral": "codestral-latest"
        }

        actual_model = mistral_model_map.get(model, model)
        console.print(f"[dim]🔥 Mistral API call: {model} → {actual_model}[/dim]")

        # Try primary client first, then rotate through backup keys
        max_retries = len(mistral_working_keys) if mistral_working_keys else 1

        for attempt in range(max_retries):
            try:
                # Use current client or create new one with backup key
                current_client = mistral_requests_client
                if attempt > 0 and mistral_working_keys and attempt < len(mistral_working_keys):
                    backup_key = mistral_working_keys[attempt][0]
                    current_client = MistralRequestsClient(backup_key)
                    console.print(f"[dim]🔄 Rotating to backup Mistral key #{mistral_working_keys[attempt][1]}[/dim]")

                # Make Mistral API call
                response = current_client.chat_completion(
                    model=actual_model,
                    messages=mistral_messages,
                    temperature=kwargs.get('temperature', 0.7),
                    max_tokens=kwargs.get('max_tokens', 2048),
                    stream=kwargs.get('stream', False)
                )

                # Update primary client if we used a backup
                if attempt > 0:
                    mistral_requests_client = current_client

                # Create a response object compatible with other providers
                class MistralResponse:
                    def __init__(self, response_data):
                        self.choices = []
                        if 'choices' in response_data and response_data['choices']:
                            choice = response_data['choices'][0]
                            if 'message' in choice:
                                self.choices.append(type('Choice', (), {
                                    'message': type('Message', (), {
                                        'content': choice['message'].get('content', ''),
                                        'role': choice['message'].get('role', 'assistant')
                                    })()
                                })())

                        self.text = response_data['choices'][0]['message']['content'] if self.choices else ""

                return MistralResponse(response)

            except Exception as retry_error:
                console.print(f"[yellow]⚠ Mistral attempt {attempt + 1} failed: {str(retry_error)[:100]}...[/yellow]")
                if attempt == max_retries - 1:
                    raise retry_error
                continue

        raise Exception("All Mistral API keys exhausted")

    except Exception as e:
        raise Exception(f"Mistral API call failed: {e}")

def make_deepseek_api_call(model: str, messages: List[Dict], tools: List = None, **kwargs):
    """Make optimized DeepSeek API call."""
    try:
        if client is None:
            raise Exception("DeepSeek client not available")

        response = client.chat.completions.create(
            model=model,
            messages=messages,
            tools=tools or [],
            tool_choice="auto" if tools else None,
            stream=kwargs.get('stream', True),
            max_tokens=kwargs.get('max_tokens', 2048),
            temperature=kwargs.get('temperature', 0.7),
            timeout=PERFORMANCE_CONFIG["api_timeout"]
        )

        return response

    except Exception as e:
        raise Exception(f"DeepSeek API call failed: {e}")

# Legacy function for backward compatibility
def make_robust_api_call(model: str, messages: List[Dict], tools: List = None, **kwargs):
    """Legacy wrapper for make_optimized_api_call."""
    return make_optimized_api_call(model, messages, tools, **kwargs)

def make_gemini_api_call(messages: List[Dict], **kwargs):
    """
    Make Gemini API call with proper message conversion.
    """
    try:
        if not gemini_client:
            raise Exception("Gemini client not available")

        # Convert messages to Gemini format
        gemini_messages = []
        for msg in messages[-10:]:  # Use last 10 messages for context
            if msg['role'] == 'system':
                gemini_messages.append({'role': 'user', 'parts': [f"System: {msg['content']}"]})
                gemini_messages.append({'role': 'model', 'parts': ['I understand the system instructions.']})
            elif msg['role'] == 'user':
                gemini_messages.append({'role': 'user', 'parts': [msg['content']]})
            elif msg['role'] == 'assistant':
                gemini_messages.append({'role': 'model', 'parts': [msg.get('content', 'I understand.')]})

        # Generate response
        response = gemini_client.generate_content(
            gemini_messages,
            generation_config={
                'temperature': kwargs.get('temperature', 0.7),
                'max_output_tokens': kwargs.get('max_tokens', 2048),
            }
        )

        return response

    except Exception as e:
        raise Exception(f"Gemini API call failed: {e}")

def determine_task_type(user_input: str) -> str:
    """Determine the type of task based on user input for optimal model routing."""
    user_lower = user_input.lower()

    # Coding-related tasks
    if any(word in user_lower for word in ['code', 'function', 'class', 'debug', 'refactor', 'implement', 'programming']):
        return "coding"

    # Reasoning-related tasks
    elif any(word in user_lower for word in ['analyze', 'explain', 'reason', 'logic', 'solve', 'problem', 'think']):
        return "reasoning"

    # Analysis tasks
    elif any(word in user_lower for word in ['review', 'assess', 'evaluate', 'examine', 'inspect', 'audit']):
        return "analysis"

    # General tasks
    else:
        return "general"

def get_model_display_name(model: str) -> str:
    """Get user-friendly display name for model with enhanced formatting."""
    display_names = {
        "deepseek-chat": "DeepSeek Engineer 🚀",
        "deepseek-reasoner": "DeepSeek Reasoner 🧠",
        "gemini-2.0-flash": "Gemini 2.0 Flash 🌟",
        "gemini-1.5-flash": "Gemini 1.5 Flash ⚡",
        "gemini-2.0-flash-exp": "Gemini 2.0 Flash 🌟",
        "mistral-large": "Mistral Large 🔥",
        "mistral-medium": "Mistral Medium 🚀",
        "mistral-small": "Mistral Small ⚡",
        "mistral-codestral": "Mistral Codestral 💻"
    }
    return display_names.get(model, f"AI Model ({model})")

def get_enhanced_prompt_indicator() -> str:
    """Get enhanced prompt indicator with model and performance info."""
    current_model = agent_state.model_context.get('current_model', DEFAULT_MODEL)
    model_emoji = {
        "deepseek-chat": "🚀",
        "deepseek-reasoner": "🧠",
        "gemini-2.0-flash": "🌟",
        "gemini-1.5-flash": "⚡",
        "mistral-large": "🔥",
        "mistral-medium": "🚀",
        "mistral-small": "⚡",
        "mistral-codestral": "💻"
    }.get(current_model, "🤖")

    # Get performance indicator
    metrics = performance_optimizer.get_performance_metrics()
    cache_indicator = "⚡" if metrics["cache_hit_rate"] > 0.5 else "🔄"

    return f"💬 {model_emoji} {cache_indicator}"

def generate_fallback_response(user_input: str, intent_analysis: Dict[str, Any]) -> str:
    """Generate intelligent fallback response when API calls fail."""
    intent_type = intent_analysis.get('intent_type', 'general')

    fallback_responses = {
        'search': f"I understand you want to search for something related to '{user_input}'. While I'm experiencing API connectivity issues, I recommend using the advanced_pattern_search() function with semantic mode for the best results.",

        'modification': f"I see you want to modify code. Even with API issues, I can suggest using smart_replace_code() with preview_only=True to safely make changes. Always backup your files first!",

        'planning': f"For planning tasks like '{user_input}', I recommend breaking it down into smaller steps. Use create_execution_plan() to structure your approach systematically.",

        'debugging': f"For debugging issues, try using analyze_code() and debug_code() functions. These can help identify problems even when I'm experiencing connectivity issues.",

        'general': f"I'm experiencing temporary API connectivity issues, but I'm still here to help! You can use any of my 50+ advanced tools directly, or try your request again in a moment."
    }

    base_response = fallback_responses.get(intent_type, fallback_responses['general'])

    # Add helpful suggestions
    suggestions = [
        "💡 Try using /help to see all available commands",
        "🔧 Use specific tool functions for immediate results",
        "🔄 Check your internet connection and try again",
        "📚 Use build_file_index() for better search performance"
    ]

    return f"{base_response}\n\n**Helpful Suggestions:**\n" + "\n".join(f"• {s}" for s in suggestions[:2])

def record_user_interaction(function_name: str, success: bool, context: Dict = None):
    """Record user interaction for learning purposes."""
    try:
        if 'learning_system' in globals() and learning_system:
            learning_system.record_interaction(function_name, context or {}, success)

        if 'context_manager' in globals() and context_manager:
            context_manager.add_context('function_call', {
                'function': function_name,
                'success': success,
                'context': context
            })

    except Exception as e:
        logger.error(f"Error recording user interaction: {e}")

def analyze_user_intent(user_input: str) -> Dict[str, Any]:
    """
    Analyze user intent to determine which tools should be used automatically.
    This makes the agent work like Augment Agent with intelligent tool selection.
    """
    intent = {
        'should_use_tools': False,
        'suggested_tools': [],
        'confidence': 0.0,
        'intent_type': 'general',
        'context_needed': []
    }

    user_lower = user_input.lower()

    # Search-related intents
    if any(word in user_lower for word in ['find', 'search', 'look for', 'locate', 'show me']):
        intent['should_use_tools'] = True
        intent['intent_type'] = 'search'
        intent['confidence'] = 0.9

        if any(word in user_lower for word in ['authentication', 'auth', 'login', 'jwt', 'token']):
            intent['suggested_tools'].append({
                'function': 'advanced_pattern_search',
                'params': {
                    'query': 'authentication jwt token validation',
                    'search_mode': 'semantic',
                    'file_types': ['.py', '.js', '.ts'],
                    'max_results': 20
                }
            })
        elif any(word in user_lower for word in ['database', 'db', 'connection', 'pool']):
            intent['suggested_tools'].append({
                'function': 'advanced_pattern_search',
                'params': {
                    'query': 'database connection pooling',
                    'search_mode': 'semantic',
                    'max_results': 15
                }
            })
        else:
            # General search
            search_terms = user_input.replace('find', '').replace('search', '').replace('look for', '').strip()
            intent['suggested_tools'].append({
                'function': 'advanced_pattern_search',
                'params': {
                    'query': search_terms,
                    'search_mode': 'hybrid',
                    'max_results': 20
                }
            })

    # Code modification intents
    elif any(word in user_lower for word in ['replace', 'change', 'update', 'modify', 'refactor']):
        intent['should_use_tools'] = True
        intent['intent_type'] = 'modification'
        intent['confidence'] = 0.8
        intent['suggested_tools'].append({
            'function': 'smart_replace_code',
            'params': {
                'preview_only': True,
                'backup_files': True
            }
        })

    # Planning intents
    elif any(word in user_lower for word in ['plan', 'implement', 'add feature', 'create system']):
        intent['should_use_tools'] = True
        intent['intent_type'] = 'planning'
        intent['confidence'] = 0.85
        intent['suggested_tools'].append({
            'function': 'create_execution_plan',
            'params': {
                'user_request': user_input,
                'strategy': 'waterfall',
                'auto_execute': False
            }
        })

    # Error/debugging intents
    elif any(word in user_lower for word in ['error', 'bug', 'fix', 'debug', 'broken']):
        intent['should_use_tools'] = True
        intent['intent_type'] = 'debugging'
        intent['confidence'] = 0.9
        intent['suggested_tools'].append({
            'function': 'fix_code_input',
            'params': {}
        })

    # Index building intents
    elif any(word in user_lower for word in ['index', 'build index', 'codebase']):
        intent['should_use_tools'] = True
        intent['intent_type'] = 'indexing'
        intent['confidence'] = 0.95
        intent['suggested_tools'].append({
            'function': 'build_file_index',
            'params': {
                'force_rebuild': False
            }
        })

    return intent

def execute_intelligent_tools(user_input: str, intent_analysis: Dict[str, Any], file_contexts: Dict[str, str]) -> str:
    """
    Execute tools intelligently based on intent analysis.
    This makes the agent proactive like Augment Agent.
    """
    results = []

    try:
        for tool_info in intent_analysis.get('suggested_tools', []):
            function_name = tool_info['function']
            params = tool_info['params']

            console.print(f"[dim]🤖 Automatically using {function_name}...[/dim]")

            # Execute the tool
            if function_name == 'advanced_pattern_search':
                result = llm_advanced_pattern_search(**params)
                results.append(f"🔍 Pattern Search Results:\n{result}")

            elif function_name == 'smart_replace_code':
                # For replace operations, we need more context from user
                results.append("🔧 Ready to perform smart code replacement. Please specify the pattern and replacement.")

            elif function_name == 'create_execution_plan':
                result = llm_create_execution_plan(**params)
                results.append(f"📋 Execution Plan Created:\n{result}")

            elif function_name == 'fix_code_input':
                results.append("🔧 Ready to fix code input. Please provide the code to fix.")

            elif function_name == 'build_file_index':
                result = llm_build_file_index(**params)
                results.append(f"📚 File Index Built:\n{result}")

        return "\n\n".join(results) if results else ""

    except Exception as e:
        logger.error(f"Error executing intelligent tools: {e}")
        return f"Error executing automatic tools: {str(e)}"

def enhance_response_with_intelligence(response: str, intent_analysis: Dict[str, Any], user_input: str) -> str:
    """
    Enhance the response with intelligent suggestions and next steps.
    This makes the agent more helpful like Augment Agent.
    """
    enhanced_parts = [response]

    try:
        # Add intelligent suggestions based on intent
        intent_type = intent_analysis.get('intent_type', 'general')

        if intent_type == 'search':
            enhanced_parts.append("\n💡 **Smart Suggestions:**")
            enhanced_parts.append("• Try using semantic search for better conceptual matches")
            enhanced_parts.append("• Use fuzzy search if you're unsure about exact spelling")
            enhanced_parts.append("• Build file index first for faster searches: `build_file_index()`")

        elif intent_type == 'modification':
            enhanced_parts.append("\n🛡️ **Safety Recommendations:**")
            enhanced_parts.append("• Always use preview mode first: `preview_only=True`")
            enhanced_parts.append("• Enable backups: `backup_files=True`")
            enhanced_parts.append("• Consider Git commit after changes: `git_commit=True`")

        elif intent_type == 'planning':
            enhanced_parts.append("\n🎯 **Next Steps:**")
            enhanced_parts.append("• Review the execution plan carefully")
            enhanced_parts.append("• Execute step by step: `execute_plan(plan_id)`")
            enhanced_parts.append("• Generate tests after implementation")

        elif intent_type == 'debugging':
            enhanced_parts.append("\n🔍 **Debugging Tips:**")
            enhanced_parts.append("• Use `analyze_code()` for detailed analysis")
            enhanced_parts.append("• Try `debug_code()` for automatic error detection")
            enhanced_parts.append("• Consider `generate_tests()` to prevent future issues")

        # Add context-aware suggestions
        if 'suggestion_engine' in globals() and suggestion_engine:
            suggestions = suggestion_engine.get_suggestions(user_input, intent_analysis)
            if suggestions:
                enhanced_parts.append("\n🧠 **AI Suggestions:**")
                for suggestion in suggestions[:3]:
                    enhanced_parts.append(f"• {suggestion}")

        return "\n".join(enhanced_parts)

    except Exception as e:
        logger.error(f"Error enhancing response: {e}")
        return response

def main() -> None:
    """Application entry point."""
    # Enhanced startup banner with performance metrics
    metrics = performance_optimizer.get_performance_metrics()

    # Get MCP status for banner
    mcp_status = mcp_client.get_server_status()
    connected_mcp = sum(1 for status in mcp_status.values() if status["status"] == "connected")
    total_mcp = len(mcp_status)

    console.print(Panel.fit(
        "[bold bright_blue]🤖 CODY - Ultra-Advanced AI Coding Assistant with MCP Integration[/bold bright_blue]\n"
        "[bold bright_green]⚡ Performance Optimized • 🧠 Enhanced Intelligence • 🔥 Multi-Provider Support • 🔌 MCP Ready[/bold bright_green]\n"
        "[dim]🚀 DeepSeek • 🌟 Gemini • 🔥 Mistral AI • 💻 Specialized Coding Models[/dim]\n"
        "[dim]✨ Intelligent Caching • 🎯 Smart Routing • 🔄 Auto-Optimization • 📊 Real-time Metrics[/dim]\n"
        "[dim]🛠️ 65+ Advanced Tools • 🤖 Autonomous Workflows • 💡 Predictive Intelligence[/dim]\n"
        f"[dim]🔌 MCP Servers: {connected_mcp}/{total_mcp} Connected • 📈 Cache: {metrics['cache_hit_rate']:.1%} • ⚡ Response: {metrics['avg_response_time']:.2f}s[/dim]\n"
        "[dim]Type /help for commands • /mcp for MCP • /switch for models • Ctrl+C to interrupt • /exit to quit[/dim]",
        border_style="bright_blue"
    ))

    # Show feature availability status
    features_status = []

    if FUZZY_AVAILABLE:
        features_status.append("[green]✓ Fuzzy Matching[/green]")
    else:
        features_status.append("[red]✗ Fuzzy Matching[/red]")

    if ADVANCED_MODULES_AVAILABLE:
        features_status.append("[green]✓ Advanced Modules[/green]")
        if nlp_processor:
            features_status.append("[green]✓ NLP Processing[/green]")
        if code_analyzer:
            features_status.append("[green]✓ Code Analysis[/green]")
        if autonomous_debugger:
            features_status.append("[green]✓ Autonomous Debugging[/green]")
        if web_search_rag:
            features_status.append("[green]✓ Web Search & RAG[/green]")
        if task_manager:
            features_status.append("[green]✓ Multi-threading[/green]")
    else:
        features_status.append("[red]✗ Advanced Modules[/red]")

    if TREE_SITTER_AVAILABLE:
        features_status.append("[green]✓ AST Parsing[/green]")
    else:
        features_status.append("[yellow]⚠ AST Parsing[/yellow]")

    if WEB_SEARCH_AVAILABLE:
        features_status.append("[green]✓ Web Dependencies[/green]")
    else:
        features_status.append("[yellow]⚠ Web Dependencies[/yellow]")

    console.print("Feature Status: " + " | ".join(features_status))

    if not ADVANCED_MODULES_AVAILABLE:
        console.print("\n[yellow]⚠ Some advanced features are disabled. Check the core/ directory and dependencies.[/yellow]")

    # Add directory structure as a system message before starting the main loop
    dir_summary = get_directory_tree_summary(base_dir)
    conversation_history.append({
        "role": "system",
        "content": f"Project directory structure at startup:\n\n{dir_summary}"
    })

    initialize_application()
    main_loop()

if __name__ == "__main__":
    main()
